[project]
name = "chat"
version = "0.1.0"
description = ""
authors = [
    {name = "jachian<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "openai (>=1.88.0,<2.0.0)",
    "openai-agents (>=0.0.19,<0.0.20)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "requests (>=2.31.0,<3.0.0)",
    "pydantic (>=2.0.0,<3.0.0)",
    "tiktoken (>=0.5.0,<1.0.0)",
    "pytest (>=7.0.0)",
    "pytest-mock (>=3.10.0)",
    "pytest-cov (>=4.0.0)",
    "pytest-env (>=0.8.0)",
    "pytest-xdist (>=3.0.0)",
]


[tool.poetry]
packages = [
    { include = "pm_ai" }
]

[tool.pytest.ini_options]
testpaths = ["pm_ai/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
# Exclude backup tests that have import issues
norecursedirs = ["pm_ai/tests/backup_old_tests"]
markers = [
    "unit: Unit tests with mocked dependencies",
    "integration: Integration tests with real API calls",
    "e2e: End-to-end tests with full system",
    "slow: Tests that take longer than 30 seconds"
]
env = [
    "PM_AI_TEST_MODE=unit"
]

[tool.coverage.run]
source = ["pm_ai"]
omit = ["pm_ai/tests/*", "pm_ai/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
