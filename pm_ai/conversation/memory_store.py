"""
Test-aware in-memory conversation storage for maintaining context across turns.
Supports both mocked unit tests and real conversation flow testing.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import logging

@dataclass
class ConversationTurn:
    """Single turn in a conversation."""
    turn_id: str
    timestamp: datetime
    user_query: str
    parsed_intent: Dict[str, Any]
    system_response: str
    tools_used: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConversationSession:
    """Complete conversation session with multiple turns."""
    session_id: str
    user_id: Optional[str]
    start_time: datetime
    last_activity: datetime
    turns: List[ConversationTurn] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)

class MemoryStore:
    """
    Test-aware in-memory storage for conversation sessions and context.

    Supports both unit testing with mocked data and integration testing
    with real conversation flows. In production, this would be replaced
    with persistent storage (Red<PERSON>, PostgreSQL, etc.).
    """

    def __init__(self, test_mode: bool = False):
        self.sessions: Dict[str, ConversationSession] = {}
        self.max_sessions = 10 if test_mode else 100  # Smaller limits for testing
        self.max_turns_per_session = 10 if test_mode else 50
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

        # Testing metrics
        self.operation_count = 0
        self.cleanup_count = 0
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create new conversation session."""
        self.operation_count += 1

        session_id = str(uuid.uuid4())
        now = datetime.now()

        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=now,
            last_activity=now
        )

        self.sessions[session_id] = session
        self._cleanup_old_sessions()

        if self.test_mode:
            self.logger.debug(f"Created test session {session_id}")

        return session_id

    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """Retrieve conversation session."""
        self.operation_count += 1
        return self.sessions.get(session_id)
    
    def add_turn(
        self,
        session_id: str,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Add new turn to conversation session."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")

        turn_id = str(uuid.uuid4())
        turn = ConversationTurn(
            turn_id=turn_id,
            timestamp=datetime.now(),
            user_query=user_query,
            parsed_intent=parsed_intent,
            system_response=system_response,
            tools_used=tools_used or [],
            metadata=metadata or {}
        )

        session.turns.append(turn)
        session.last_activity = datetime.now()

        # Limit turns per session
        if len(session.turns) > self.max_turns_per_session:
            session.turns = session.turns[-self.max_turns_per_session:]

        if self.test_mode:
            self.logger.debug(f"Added turn {turn_id} to session {session_id}")

        return turn_id

    def update_context(self, session_id: str, context_updates: Dict[str, Any]):
        """Update session context."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if session:
            session.context.update(context_updates)
            session.last_activity = datetime.now()

            if self.test_mode:
                self.logger.debug(f"Updated context for session {session_id}")

    def get_recent_turns(self, session_id: str, count: int = 5) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if not session:
            return []

        return session.turns[-count:] if session.turns else []
    
    def _cleanup_old_sessions(self):
        """Remove oldest sessions if limit exceeded."""
        if len(self.sessions) > self.max_sessions:
            self.cleanup_count += 1

            # Sort by last activity and remove oldest
            sorted_sessions = sorted(
                self.sessions.items(),
                key=lambda x: x[1].last_activity
            )

            sessions_to_remove = len(self.sessions) - self.max_sessions
            for session_id, _ in sorted_sessions[:sessions_to_remove]:
                del self.sessions[session_id]

            if self.test_mode:
                self.logger.debug(f"Cleaned up {sessions_to_remove} old sessions")

    # Testing utility methods
    def get_operation_count(self) -> int:
        """Get number of operations performed (for testing)."""
        return self.operation_count

    def get_cleanup_count(self) -> int:
        """Get number of cleanup operations performed (for testing)."""
        return self.cleanup_count

    def get_session_count(self) -> int:
        """Get current number of sessions (for testing)."""
        return len(self.sessions)

    def clear_all_sessions(self):
        """Clear all sessions (for testing)."""
        self.sessions.clear()
        if self.test_mode:
            self.logger.debug("Cleared all sessions for testing")
