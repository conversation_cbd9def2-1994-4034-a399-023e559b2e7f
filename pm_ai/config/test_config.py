"""
Test configuration management to prevent API key handling conflicts.
"""
import os
from typing import Optional

class TestConfig:
    """Test-specific configuration management."""
    
    @staticmethod
    def get_test_mode() -> str:
        """
        Determine test mode from environment.
        
        Returns:
            'unit' - Use mocks, no API calls
            'integration' - Use real API with test key
            'e2e' - Use real API with full system
        """
        return os.getenv("PM_AI_TEST_MODE", "unit")
    
    @staticmethod
    def get_openai_key() -> Optional[str]:
        """Get OpenAI API key based on test mode."""
        test_mode = TestConfig.get_test_mode()
        
        if test_mode == "unit":
            return None  # No API key needed for mocked tests
        elif test_mode in ["integration", "e2e"]:
            # Use test-specific key or fall back to main key
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")
        
        return os.getenv("OPENAI_API_KEY")
    
    @staticmethod
    def should_use_real_api() -> bool:
        """Determine if tests should use real API calls."""
        return TestConfig.get_test_mode() in ["integration", "e2e"]
    
    @staticmethod
    def get_max_api_calls() -> int:
        """Get maximum API calls allowed per test run."""
        return int(os.getenv("PM_AI_MAX_API_CALLS_PER_TEST_RUN", "50"))
    
    @staticmethod
    def get_openai_model() -> str:
        """Get OpenAI model for testing."""
        return os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    
    @staticmethod
    def get_max_tokens() -> int:
        """Get maximum tokens for testing."""
        return int(os.getenv("OPENAI_MAX_TOKENS", "1000"))
    
    @staticmethod
    def get_temperature() -> float:
        """Get temperature for testing."""
        return float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
    
    @staticmethod
    def is_debug_mode() -> bool:
        """Check if debug mode is enabled."""
        return os.getenv("PM_AI_DEBUG", "false").lower() == "true"
    
    @staticmethod
    def get_test_timeout() -> int:
        """Get test timeout in seconds."""
        return int(os.getenv("PM_AI_TEST_TIMEOUT", "30"))
    
    @staticmethod
    def get_mock_delay() -> float:
        """Get simulated delay for mock responses."""
        return float(os.getenv("PM_AI_MOCK_DELAY", "0.1"))
