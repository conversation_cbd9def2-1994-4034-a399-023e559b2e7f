"""
Unit tests for advanced response synthesizer with mocked dependencies.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.advanced_synthesizer import (
    AdvancedSynthesizer, SynthesisContext, SynthesisMode, ResponseSection
)
from pm_ai.intelligence.reasoning_engine import <PERSON><PERSON><PERSON><PERSON><PERSON>, ReasoningNode, ReasoningStep
from pm_ai.intelligence.query_planner import QueryPlan, ExecutableTask, TaskType
from pm_ai.intelligence.intent_parser import QueryIntent
from pm_ai.integration.tool_orchestrator import ToolResult

@pytest.mark.unit
class TestAdvancedSynthesizerUnit:
    """Unit tests for advanced synthesizer with mocked dependencies."""

    def test_advanced_synthesizer_initialization(self, mock_llm_engine):
        """Test advanced synthesizer initializes correctly."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        assert synthesizer.llm_engine is not None
        assert synthesizer.test_mode is True
        assert synthesizer.get_synthesis_count() == 0
        assert len(synthesizer.response_templates) > 0

    def test_simple_synthesis_mode(self, mock_llm_engine):
        """Test simple synthesis for basic queries."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Property P123 has 85% occupancy rate and $12,000 revenue."
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Create simple context
        tool_results = [
            ToolResult("get_property_metrics", True, {"occupancy_rate": 85, "revenue": 12000}, execution_time=0.5)
        ]
        
        context = SynthesisContext(
            query="How is P123 performing?",
            intent="property_performance",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.SIMPLE
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert isinstance(response, str)
        assert len(response) > 20
        assert synthesizer.get_synthesis_count() == 1
        assert synthesizer.get_simple_synthesis_count() == 1

    def test_complex_synthesis_with_reasoning_chain(self, mock_llm_engine):
        """Test complex synthesis with reasoning chain."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Based on comprehensive analysis, Property P123 shows strong performance with 85% occupancy rate, indicating effective market positioning and pricing strategy."
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Create reasoning chain
        chain = ReasoningChain("chain_1", "How is P123 performing?", QueryIntent.PROPERTY_PERFORMANCE)
        
        # Add completed reasoning nodes
        data_node = ReasoningNode("step_1", ReasoningStep.DATA_COLLECTION, "Collect data")
        data_node.completed = True
        data_node.data_outputs = {"collected_data": {"occupancy_rate": 85}}
        
        analysis_node = ReasoningNode("step_2", ReasoningStep.ANALYSIS, "Analyze data")
        analysis_node.completed = True
        analysis_node.data_outputs = {"analysis_result": "Strong performance indicators"}
        
        synthesis_node = ReasoningNode("step_3", ReasoningStep.SYNTHESIS, "Synthesize findings")
        synthesis_node.completed = True
        synthesis_node.data_outputs = {"synthesis_result": "Comprehensive insights"}
        
        chain.nodes = [data_node, analysis_node, synthesis_node]
        chain.completed = True
        chain.confidence = 0.9
        
        # Create complex context
        context = SynthesisContext(
            query="Provide comprehensive analysis of P123",
            intent="property_performance",
            confidence=0.9,
            reasoning_chain=chain,
            synthesis_mode=SynthesisMode.ANALYTICAL,
            required_sections=[ResponseSection.KEY_FINDINGS, ResponseSection.RECOMMENDATIONS]
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert isinstance(response, str)
        assert len(response) > 50
        assert synthesizer.get_synthesis_count() == 1
        assert synthesizer.get_complex_synthesis_count() == 1

    def test_executive_synthesis_mode(self, mock_llm_engine):
        """Test executive synthesis mode."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Executive Summary: Property P123 performing above market average.\n\nKey Metrics:\n- Occupancy: 85%\n- Revenue: $12,000\n\nRecommendations:\n- Maintain current pricing strategy"
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        context = SynthesisContext(
            query="Give me an executive summary of P123",
            intent="property_performance",
            confidence=0.95,
            synthesis_mode=SynthesisMode.EXECUTIVE
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert "Executive Summary" in response or "executive summary" in response.lower()
        assert len(response) > 30

    def test_synthesis_with_query_plan(self, mock_llm_engine):
        """Test synthesis with query plan results."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Based on the executed query plan, here's a comprehensive analysis of your property performance."
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Create query plan
        plan = QueryPlan("plan_1", "Analyze P123", QueryIntent.PROPERTY_PERFORMANCE)
        
        # Add completed tasks
        task1 = ExecutableTask("task_1", TaskType.DATA_RETRIEVAL, "Get data")
        task1.completed = True
        task1.result = {"occupancy_rate": 85, "revenue": 12000}
        
        task2 = ExecutableTask("task_2", TaskType.ANALYSIS, "Analyze data")
        task2.completed = True
        task2.result = {"insights": "Strong performance"}
        
        plan.tasks = [task1, task2]
        plan.completed = True
        plan.complexity_score = 6.0
        
        context = SynthesisContext(
            query="Comprehensive analysis of P123",
            intent="property_performance",
            confidence=0.9,
            query_plan=plan,
            synthesis_mode=SynthesisMode.ANALYTICAL
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert isinstance(response, str)
        assert len(response) > 30

    def test_complexity_determination(self, mock_llm_engine):
        """Test complexity determination logic."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Simple context
        simple_context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.9,
            synthesis_mode=SynthesisMode.SIMPLE
        )
        
        # Complex context
        complex_context = SynthesisContext(
            query="Comprehensive analysis",
            intent="recommendations",
            confidence=0.9,
            synthesis_mode=SynthesisMode.ANALYTICAL,
            required_sections=[
                ResponseSection.EXECUTIVE_SUMMARY,
                ResponseSection.KEY_FINDINGS,
                ResponseSection.RECOMMENDATIONS,
                ResponseSection.NEXT_STEPS
            ]
        )
        
        assert not synthesizer._is_complex_synthesis(simple_context)
        assert synthesizer._is_complex_synthesis(complex_context)

    def test_confidence_based_response_adjustment(self, mock_llm_engine):
        """Test response adjustment based on confidence levels."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Analysis shows property performance metrics."
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Low confidence context
        low_confidence_context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.5
        )
        
        response = synthesizer.synthesize_advanced_response(low_confidence_context)
        
        # Should include confidence disclaimer
        assert "limited data" in response.lower() or "note:" in response.lower()

    def test_error_handling(self, mock_llm_engine):
        """Test error handling in synthesis."""
        mock_llm_engine.create_completion = Mock(side_effect=Exception("LLM Error"))
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.9
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert "error" in response.lower() or "apologize" in response.lower()
        assert synthesizer.get_error_count() == 1

    def test_tool_results_formatting(self, mock_llm_engine):
        """Test formatting of tool results."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        tool_results = [
            ToolResult("get_property_metrics", True, {"occupancy_rate": 85}, execution_time=0.5),
            ToolResult("get_property_comparisons", False, None, "Property not found", execution_time=1.0)
        ]
        
        # Test simple formatting
        simple_formatted = synthesizer._format_tool_results_simple(tool_results)
        assert "get_property_metrics" in simple_formatted
        assert "Success" in simple_formatted
        assert "Failed" in simple_formatted
        
        # Test detailed formatting
        detailed_formatted = synthesizer._format_tool_results_detailed(tool_results)
        assert "Tool 1:" in detailed_formatted
        assert "Tool 2:" in detailed_formatted
        assert "Execution Time:" in detailed_formatted

    def test_reasoning_chain_formatting(self, mock_llm_engine):
        """Test formatting of reasoning chain results."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Create reasoning chain
        chain = ReasoningChain("chain_1", "Test query", QueryIntent.PROPERTY_PERFORMANCE)
        
        node = ReasoningNode("step_1", ReasoningStep.DATA_COLLECTION, "Collect data")
        node.completed = True
        node.data_outputs = {"data": "test_data"}
        
        chain.nodes = [node]
        chain.completed = True
        chain.confidence = 0.8
        
        formatted = synthesizer._format_reasoning_chain(chain)
        
        assert "Chain ID: chain_1" in formatted
        assert "Completed: True" in formatted
        assert "Confidence: 0.80" in formatted
        assert "data_collection" in formatted

    def test_query_plan_formatting(self, mock_llm_engine):
        """Test formatting of query plan results."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Create query plan
        plan = QueryPlan("plan_1", "Test query", QueryIntent.PROPERTY_PERFORMANCE)
        
        task = ExecutableTask("task_1", TaskType.DATA_RETRIEVAL, "Get data")
        task.completed = True
        task.result = {"data": "test_result"}
        
        plan.tasks = [task]
        plan.completed = True
        plan.complexity_score = 5.0
        plan.estimated_total_duration = 2.5
        
        formatted = synthesizer._format_query_plan(plan)
        
        assert "Plan ID: plan_1" in formatted
        assert "Completed: True" in formatted
        assert "Complexity Score: 5.0" in formatted
        assert "Estimated Duration: 2.5s" in formatted

    def test_response_templates(self, mock_llm_engine):
        """Test response templates for different modes."""
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Check all synthesis modes have templates
        for mode in SynthesisMode:
            assert mode in synthesizer.response_templates
            template = synthesizer.response_templates[mode]
            assert isinstance(template, str)
            assert len(template) > 20

    def test_conversation_context_integration(self, mock_llm_engine):
        """Test integration of conversation context."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Based on our previous conversation about P123, here's the updated analysis."
        })
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        conversation_context = {
            "history": [
                {"user": "How is P123?", "system": "P123 is performing well."},
                {"user": "What about compared to last month?", "system": "Improvement noted."}
            ]
        }
        
        context = SynthesisContext(
            query="Any new insights?",
            intent="property_performance",
            confidence=0.9,
            conversation_context=conversation_context
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        assert isinstance(response, str)
        assert len(response) > 20

    def test_metrics_tracking(self, mock_llm_engine):
        """Test metrics tracking functionality."""
        mock_llm_engine.create_completion = Mock(return_value={"content": "Test response"})
        
        synthesizer = AdvancedSynthesizer(mock_llm_engine, test_mode=True)
        
        # Perform multiple syntheses
        simple_context = SynthesisContext("test", "test", 0.9, synthesis_mode=SynthesisMode.SIMPLE)
        complex_context = SynthesisContext("test", "test", 0.9, synthesis_mode=SynthesisMode.ANALYTICAL, required_sections=[ResponseSection.KEY_FINDINGS, ResponseSection.RECOMMENDATIONS, ResponseSection.NEXT_STEPS, ResponseSection.RISK_ASSESSMENT])
        
        synthesizer.synthesize_advanced_response(simple_context)
        synthesizer.synthesize_advanced_response(complex_context)
        
        assert synthesizer.get_synthesis_count() == 2
        assert synthesizer.get_simple_synthesis_count() == 1
        assert synthesizer.get_complex_synthesis_count() == 1
        
        # Reset metrics
        synthesizer.reset_metrics()
        assert synthesizer.get_synthesis_count() == 0
