"""
Unit tests for context-aware error handler with mocked dependencies.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.error_handler import (
    ContextAwareError<PERSON><PERSON><PERSON>, ErrorContext, RecoveryPlan, 
    ErrorType, ErrorSeverity, RecoveryStrategy
)
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.unit
class TestErrorHandlerUnit:
    """Unit tests for error handler with mocked dependencies."""

    def test_error_handler_initialization(self, mock_llm_engine):
        """Test error handler initializes correctly."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        assert handler.llm_engine is not None
        assert handler.test_mode is True
        assert handler.get_errors_handled() == 0
        assert len(handler.error_patterns) > 0
        assert len(handler.recovery_strategies) > 0

    def test_handle_parsing_error(self, mock_llm_engine):
        """Test handling of parsing errors."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "I need clarification on which property you're asking about. Could you specify a property ID like P123?"
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="How is my property doing?",
            error_message="Could not parse property identifier"
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        assert isinstance(recovery_plan, RecoveryPlan)
        assert recovery_plan.strategy in [RecoveryStrategy.CLARIFY_QUERY, RecoveryStrategy.PROVIDE_EXAMPLES]
        assert len(recovery_plan.clarification_message) > 20
        assert handler.get_errors_handled() == 1

    def test_handle_tool_execution_error(self, mock_llm_engine):
        """Test handling of tool execution errors."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "It looks like property P999 wasn't found. Here are some alternatives you can try: check the property ID or try P123, P456, or P789."
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.TOOL_EXECUTION_ERROR,
            severity=ErrorSeverity.HIGH,
            original_query="How is P999 performing?",
            error_message="Property P999 not found in database"
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        assert recovery_plan.strategy in [RecoveryStrategy.SUGGEST_ALTERNATIVES, RecoveryStrategy.RETRY_WITH_DEFAULTS]
        assert "P999" in recovery_plan.clarification_message or "not found" in recovery_plan.clarification_message.lower()

    def test_handle_insufficient_context_error(self, mock_llm_engine):
        """Test handling of insufficient context errors."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "To provide property performance information, I need you to specify which property you're interested in. Please provide a property ID."
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.INSUFFICIENT_CONTEXT,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me performance",
            error_message="Missing property identifier"
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        assert recovery_plan.strategy in [RecoveryStrategy.REQUEST_PARAMETERS, RecoveryStrategy.PROVIDE_EXAMPLES]
        assert "property" in recovery_plan.clarification_message.lower()

    def test_error_severity_classification(self, mock_llm_engine):
        """Test error severity classification."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Test parsing error classification
        parsing_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=None,  # Will be classified
            original_query="unclear query",
            error_message="Could not understand request"
        )
        
        severity = handler._classify_error_severity(parsing_context)
        assert severity == ErrorSeverity.MEDIUM
        
        # Test system error classification
        system_context = ErrorContext(
            error_type=ErrorType.SYSTEM_ERROR,
            severity=None,
            original_query="any query",
            error_message="Internal system error occurred"
        )
        
        severity = handler._classify_error_severity(system_context)
        assert severity == ErrorSeverity.CRITICAL

    def test_recovery_strategy_selection(self, mock_llm_engine):
        """Test recovery strategy selection logic."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Test strategy selection for ambiguous query
        ambiguous_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me stuff",
            error_message="Query too ambiguous"
        )
        
        strategy = handler._select_recovery_strategy(ambiguous_context)
        assert strategy in [RecoveryStrategy.CLARIFY_QUERY, RecoveryStrategy.SUGGEST_ALTERNATIVES]
        
        # Test strategy escalation with multiple recovery attempts
        repeated_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="unclear query",
            error_message="Still unclear",
            recovery_attempts=2
        )
        
        strategy = handler._select_recovery_strategy(repeated_context)
        assert strategy in [RecoveryStrategy.PROVIDE_EXAMPLES, RecoveryStrategy.FALLBACK_RESPONSE]

    def test_conversation_history_consideration(self, mock_llm_engine):
        """Test that conversation history affects recovery strategy."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Let me suggest some alternative approaches since we've had some confusion."
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Context with error history
        error_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me more",
            error_message="Still ambiguous",
            conversation_history=[
                {"user": "Show me data", "system": "I need clarification..."},
                {"user": "Show me info", "system": "Could you specify..."},
                {"user": "Show me more", "system": "Error occurred"}
            ]
        )
        
        strategy = handler._select_recovery_strategy(error_context)
        # Should prefer alternatives after repeated clarification attempts
        assert strategy == RecoveryStrategy.SUGGEST_ALTERNATIVES

    def test_recovery_plan_enhancement(self, mock_llm_engine):
        """Test enhancement of recovery plans with context-specific information."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Please specify which property you're asking about."
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Test ambiguous query enhancement
        ambiguous_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me performance",
            error_message="Ambiguous property reference"
        )
        
        recovery_plan = handler.handle_error(ambiguous_context)
        
        # Should have suggested queries for ambiguous queries
        assert len(recovery_plan.suggested_queries) > 0
        assert any("P123" in query for query in recovery_plan.suggested_queries)

    def test_missing_parameters_recovery(self, mock_llm_engine):
        """Test recovery for missing parameters errors."""
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "To help you with this request, I need the property ID, time period, and specific metrics you're interested in."
        })
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        missing_params_context = ErrorContext(
            error_type=ErrorType.MISSING_PARAMETERS,
            severity=ErrorSeverity.MEDIUM,
            original_query="Get metrics",
            error_message="Missing required parameters"
        )
        
        recovery_plan = handler.handle_error(missing_params_context)
        
        # Should have required parameters listed
        assert len(recovery_plan.required_parameters) > 0
        assert "property_id" in recovery_plan.required_parameters

    def test_fallback_recovery_plan(self, mock_llm_engine):
        """Test fallback recovery plan generation."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.SYSTEM_ERROR,
            severity=ErrorSeverity.CRITICAL,
            original_query="Any query",
            error_message="Critical system failure"
        )
        
        fallback_plan = handler._generate_fallback_recovery_plan(error_context)
        
        assert fallback_plan.strategy == RecoveryStrategy.FALLBACK_RESPONSE
        assert "apologize" in fallback_plan.clarification_message.lower()
        assert fallback_plan.confidence < 0.5

    def test_error_handling_failure_recovery(self, mock_llm_engine):
        """Test recovery when error handling itself fails."""
        # Mock LLM to raise exception
        mock_llm_engine.create_completion = Mock(side_effect=Exception("LLM Error"))
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="Test query",
            error_message="Test error"
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Should still return a recovery plan
        assert isinstance(recovery_plan, RecoveryPlan)
        assert recovery_plan.strategy == RecoveryStrategy.FALLBACK_RESPONSE
        assert handler.get_failed_recoveries() == 1

    def test_recovery_system_prompt_building(self, mock_llm_engine):
        """Test building of recovery system prompts."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Test different strategy prompts
        clarify_prompt = handler._build_recovery_system_prompt(RecoveryStrategy.CLARIFY_QUERY)
        assert "clarifying questions" in clarify_prompt.lower()
        
        alternatives_prompt = handler._build_recovery_system_prompt(RecoveryStrategy.SUGGEST_ALTERNATIVES)
        assert "alternative" in alternatives_prompt.lower()
        
        examples_prompt = handler._build_recovery_system_prompt(RecoveryStrategy.PROVIDE_EXAMPLES)
        assert "examples" in examples_prompt.lower()

    def test_recovery_user_prompt_building(self, mock_llm_engine):
        """Test building of recovery user prompts."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.7,
            parameters={"property_ids": ["P123"]}
        )
        
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="How is P123?",
            error_message="Parse error",
            parsed_intent=parsed_intent,
            conversation_history=[
                {"user": "Previous query", "system": "Previous response"}
            ]
        )
        
        user_prompt = handler._build_recovery_user_prompt(error_context, RecoveryStrategy.CLARIFY_QUERY)
        
        assert "How is P123?" in user_prompt
        assert "Parse error" in user_prompt
        assert "property_performance" in user_prompt.lower()
        assert "Previous query" in user_prompt

    def test_metrics_tracking(self, mock_llm_engine):
        """Test error handling metrics tracking."""
        mock_llm_engine.create_completion = Mock(return_value={"content": "Recovery response"})
        
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Handle multiple errors
        for i in range(3):
            error_context = ErrorContext(
                error_type=ErrorType.PARSING_ERROR,
                severity=ErrorSeverity.MEDIUM,
                original_query=f"Query {i}",
                error_message=f"Error {i}"
            )
            handler.handle_error(error_context)
        
        assert handler.get_errors_handled() == 3
        
        # Test metrics reset
        handler.reset_metrics()
        assert handler.get_errors_handled() == 0
        assert handler.get_successful_recoveries() == 0
        assert handler.get_failed_recoveries() == 0

    def test_recovery_success_rate_calculation(self, mock_llm_engine):
        """Test recovery success rate calculation."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Simulate some recoveries
        handler.errors_handled = 10
        handler.successful_recoveries = 7
        handler.failed_recoveries = 3
        
        assert handler.get_recovery_success_rate() == 0.7
        
        # Test with no errors
        handler.reset_metrics()
        assert handler.get_recovery_success_rate() == 0.0

    def test_error_pattern_matching(self, mock_llm_engine):
        """Test error pattern matching and classification."""
        handler = ContextAwareErrorHandler(mock_llm_engine, test_mode=True)
        
        # Test that error patterns are properly initialized
        assert ErrorType.PARSING_ERROR in handler.error_patterns
        assert ErrorType.TOOL_EXECUTION_ERROR in handler.error_patterns
        assert ErrorType.INSUFFICIENT_CONTEXT in handler.error_patterns
        
        # Test pattern structure
        parsing_pattern = handler.error_patterns[ErrorType.PARSING_ERROR]
        assert "keywords" in parsing_pattern
        assert "default_severity" in parsing_pattern
        assert "recovery_strategies" in parsing_pattern
        assert len(parsing_pattern["recovery_strategies"]) > 0
