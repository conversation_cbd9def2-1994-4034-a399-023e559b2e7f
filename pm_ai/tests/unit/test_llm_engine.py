"""
Unit tests for LLM engine with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, patch
import os

from pm_ai.intelligence.llm_engine import LL<PERSON>ng<PERSON>, LLMConfig

@pytest.mark.unit
class TestLLMEngineUnit:
    """Unit tests for LLM engine with mocked dependencies."""

    def test_initialization_with_mock(self, mock_openai_client):
        """Test LLM engine initializes correctly with mock client."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        assert engine.client is not None
        assert engine.is_mocked is True
        assert engine.config.test_mode is True

    def test_create_completion_mocked(self, mock_openai_client):
        """Test completion creation with mocked client."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        messages = [{"role": "user", "content": "Test message"}]
        result = engine.create_completion(messages)

        assert result["content"] == "Mock response for testing"
        assert result["usage"]["total_tokens"] == 15
        # Mocked calls should not increment API counter
        assert engine.get_api_call_count() == 0

    def test_function_calling_mock(self, mock_openai_client):
        """Test function calling with mocked response."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        messages = [{"role": "user", "content": "How is P123 performing?"}]
        functions = [{"name": "extract_intent", "description": "Extract intent"}]

        result = engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Verify function call was processed
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    def test_token_counting_fallback(self, mock_openai_client):
        """Test token counting with fallback for mocked engine."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        text = "Hello world test"
        count = engine.count_tokens(text)

        # Should use fallback estimation
        assert count > 0
        assert isinstance(count, int)

    def test_api_call_tracking(self, mock_openai_client):
        """Test API call counting for cost control."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        initial_count = engine.get_api_call_count()

        messages = [{"role": "user", "content": "Test"}]
        engine.create_completion(messages)

        # Mocked calls should not increment counter
        assert engine.get_api_call_count() == initial_count

    def test_no_client_error(self):
        """Test error when no client is available."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=None)
        engine.client = None  # Force no client
        
        messages = [{"role": "user", "content": "Test"}]
        
        with pytest.raises(ValueError, match="No OpenAI client available"):
            engine.create_completion(messages)

    def test_production_mode_requires_api_key(self):
        """Test that production mode requires API key."""
        config = LLMConfig(test_mode=False)
        
        # Mock environment to have no API key
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="OPENAI_API_KEY environment variable is required"):
                LLMEngine(config=config)

    def test_mock_response_without_function_call(self, mock_openai_client):
        """Test mock response when no function calling is requested."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        messages = [{"role": "user", "content": "Hello"}]
        result = engine.create_completion(messages)
        
        assert result["content"] == "Mock response for testing"
        assert result["function_call"] is None
        assert result["finish_reason"] == "stop"

    def test_mock_response_with_function_call(self, mock_openai_client):
        """Test mock response when function calling is requested."""
        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config, mock_client=mock_openai_client)
        
        messages = [{"role": "user", "content": "Extract intent"}]
        functions = [{"name": "test_function", "description": "Test"}]
        
        result = engine.create_completion(messages, functions, "test_function")
        
        assert result["content"] is None
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "test_function"
        assert "mock" in result["function_call"]["arguments"]
