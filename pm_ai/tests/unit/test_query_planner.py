"""
Unit tests for autonomous query planning system with mocked dependencies.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.query_planner import (
    QueryPlanner, QueryPlan, ExecutableTask, TaskType, TaskPriority
)
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.unit
class TestQueryPlannerUnit:
    """Unit tests for query planner with mocked dependencies."""

    def test_query_planner_initialization(self, mock_llm_engine):
        """Test query planner initializes correctly."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        assert planner.llm_engine is not None
        assert planner.test_mode is True
        assert planner.get_plans_created() == 0
        assert len(planner.planning_patterns) > 0
        assert len(planner.tool_capabilities) > 0

    def test_create_query_plan_property_performance(self, mock_llm_engine):
        """Test creating query plan for property performance."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "original_query": "How is P123 performing?"}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        assert plan.plan_id.startswith("plan_")
        assert plan.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert len(plan.tasks) == 3  # DATA_RETRIEVAL, ANALYSIS, SYNTHESIS
        assert len(plan.execution_order) == len(plan.tasks)
        assert plan.estimated_total_duration > 0
        assert plan.complexity_score > 0
        assert 0 <= plan.confidence <= 1
        assert planner.get_plans_created() == 1

    def test_create_query_plan_recommendations(self, mock_llm_engine):
        """Test creating query plan for recommendations."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P456"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        assert len(plan.tasks) >= 5  # DATA_RETRIEVAL, ANALYSIS, CALCULATION, SYNTHESIS, VALIDATION (may have extra validation)
        
        # Check task types
        task_types = [task.task_type for task in plan.tasks]
        assert TaskType.DATA_RETRIEVAL in task_types
        assert TaskType.ANALYSIS in task_types
        assert TaskType.CALCULATION in task_types
        assert TaskType.SYNTHESIS in task_types
        assert TaskType.VALIDATION in task_types

    def test_task_creation_with_tools(self, mock_llm_engine):
        """Test task creation with tool assignment."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        task = planner._create_task(
            "test_task", TaskType.DATA_RETRIEVAL, parsed_intent
        )
        
        assert task.task_id == "test_task"
        assert task.task_type == TaskType.DATA_RETRIEVAL
        assert task.tool_name == "get_property_metrics"
        assert task.priority == TaskPriority.CRITICAL  # Data retrieval is critical
        assert task.estimated_duration > 0
        assert not task.completed

    def test_task_dependencies_setting(self, mock_llm_engine):
        """Test that task dependencies are set correctly."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # First task should have no dependencies
        assert len(plan.tasks[0].dependencies) == 0
        
        # Second task should depend on first
        assert plan.tasks[0].task_id in plan.tasks[1].dependencies
        
        # Synthesis task should depend on analysis tasks
        synthesis_task = next(task for task in plan.tasks if task.task_type == TaskType.SYNTHESIS)
        assert len(synthesis_task.dependencies) > 1

    def test_execution_order_optimization(self, mock_llm_engine):
        """Test execution order optimization."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={"property_ids": ["P123", "P456"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # Execution order should respect dependencies
        executed_tasks = []
        for task_id in plan.execution_order:
            task = planner._get_task_by_id(plan, task_id)
            
            # All dependencies should be executed before this task
            for dep_id in task.dependencies:
                assert dep_id in [t.task_id for t in executed_tasks]
            
            executed_tasks.append(task)

    def test_complexity_score_calculation(self, mock_llm_engine):
        """Test complexity score calculation."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        # Simple query
        simple_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        simple_plan = planner.create_query_plan(simple_intent)
        
        # Complex query
        complex_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P123", "P456", "P789"]}
        )
        complex_plan = planner.create_query_plan(complex_intent)
        
        # Complex plan should have higher complexity score
        assert complex_plan.complexity_score > simple_plan.complexity_score

    def test_duration_estimation(self, mock_llm_engine):
        """Test duration estimation for query plans."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # Should have reasonable duration estimate
        assert plan.estimated_total_duration > 0
        assert plan.estimated_total_duration < 10  # Should be under 10 seconds
        
        # Duration should be sum of task durations (considering parallelism)
        total_task_duration = sum(task.estimated_duration for task in plan.tasks)
        assert plan.estimated_total_duration <= total_task_duration

    def test_confidence_calculation(self, mock_llm_engine):
        """Test confidence calculation for query plans."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        # High confidence input
        high_confidence_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.95,
            parameters={"property_ids": ["P123"]}
        )
        high_plan = planner.create_query_plan(high_confidence_intent)
        
        # Low confidence input
        low_confidence_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.5,
            parameters={"property_ids": ["P123"]}
        )
        low_plan = planner.create_query_plan(low_confidence_intent)
        
        # High confidence input should result in higher plan confidence
        assert high_plan.confidence > low_plan.confidence

    def test_execute_query_plan_success(self, mock_llm_engine):
        """Test successful execution of query plan."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # Mock tools
        mock_tool = Mock(return_value={"occupancy_rate": 85, "revenue": 12000})
        available_tools = {"get_property_metrics": mock_tool}
        
        # Execute plan
        result_plan = planner.execute_query_plan(plan, available_tools)
        
        assert result_plan.completed is True
        assert result_plan.final_result is not None
        assert "task_results" in result_plan.final_result
        assert planner.get_successful_plans() == 1

    def test_execute_query_plan_failure(self, mock_llm_engine):
        """Test handling of query plan execution failure."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # Mock failing tool
        mock_tool = Mock(side_effect=Exception("Tool Error"))
        available_tools = {"get_property_metrics": mock_tool}
        
        # Execute plan
        result_plan = planner.execute_query_plan(plan, available_tools)
        
        # Should handle errors gracefully
        failed_tasks = [task for task in result_plan.tasks if task.error_message]
        assert len(failed_tasks) > 0
        assert planner.get_failed_plans() == 0  # Plan completed but with task failures

    def test_parallel_task_identification(self, mock_llm_engine):
        """Test identification of parallel execution opportunities."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        # Create tasks with different dependency patterns
        task1 = ExecutableTask("task1", TaskType.DATA_RETRIEVAL, "Get data 1")
        task2 = ExecutableTask("task2", TaskType.DATA_RETRIEVAL, "Get data 2")
        task3 = ExecutableTask("task3", TaskType.ANALYSIS, "Analyze", dependencies=["task1", "task2"])
        
        tasks = [task1, task2, task3]
        
        parallel_groups = planner._identify_parallel_groups(tasks)
        
        # task1 and task2 should be in the same parallel group
        assert len(parallel_groups) >= 2
        first_group = parallel_groups[0]
        assert len(first_group) == 2  # task1 and task2 can run in parallel

    def test_tool_parameter_extraction(self, mock_llm_engine):
        """Test extraction of tool parameters from tasks."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        task = ExecutableTask(
            "test_task",
            TaskType.DATA_RETRIEVAL,
            "Test task",
            parameters={
                "property_ids": ["P123"],
                "time_period": "last_30_days",
                "metrics": ["occupancy_rate"]
            }
        )
        
        params = planner._extract_tool_parameters(task)
        
        assert params["property_id"] == "P123"
        assert params["time_period"] == "last_30_days"
        assert params["metrics"] == ["occupancy_rate"]

    def test_plan_structure_validation(self, mock_llm_engine):
        """Test validation of plan structure."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        # Good structure: has data retrieval and synthesis
        good_tasks = [
            ExecutableTask("task1", TaskType.DATA_RETRIEVAL, "Get data"),
            ExecutableTask("task2", TaskType.ANALYSIS, "Analyze"),
            ExecutableTask("task3", TaskType.SYNTHESIS, "Synthesize")
        ]
        
        # Bad structure: missing synthesis
        bad_tasks = [
            ExecutableTask("task1", TaskType.DATA_RETRIEVAL, "Get data"),
            ExecutableTask("task2", TaskType.ANALYSIS, "Analyze")
        ]
        
        assert planner._has_good_structure(good_tasks) is True
        assert planner._has_good_structure(bad_tasks) is False

    def test_multiple_property_optimization(self, mock_llm_engine):
        """Test optimization for multiple properties."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={"property_ids": ["P123", "P456", "P789"]}
        )
        
        plan = planner.create_query_plan(parsed_intent)
        
        # Should have additional parallel tasks for multiple properties
        data_retrieval_tasks = [task for task in plan.tasks if task.task_type == TaskType.DATA_RETRIEVAL]
        assert len(data_retrieval_tasks) > 1  # Should have parallel data retrieval

    def test_metrics_tracking(self, mock_llm_engine):
        """Test metrics tracking functionality."""
        planner = QueryPlanner(mock_llm_engine, test_mode=True)
        
        # Create multiple plans
        for i in range(3):
            parsed_intent = ParsedIntent(
                intent=QueryIntent.PROPERTY_PERFORMANCE,
                confidence=0.9,
                parameters={"property_ids": [f"P{i}"]}
            )
            planner.create_query_plan(parsed_intent)
        
        assert planner.get_plans_created() == 3
        
        # Reset metrics
        planner.reset_metrics()
        assert planner.get_plans_created() == 0
        assert planner.get_successful_plans() == 0
        assert planner.get_failed_plans() == 0
