"""
Unit tests for tool orchestration with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, MagicMock
import time

from pm_ai.integration.tool_orchestrator import ToolOrchestrator, ToolResult
from pm_ai.integration.result_synthesizer import ResultSynthesizer
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.unit
class TestToolOrchestrationUnit:
    """Unit tests for tool orchestration with mocked dependencies."""

    def test_tool_orchestrator_initialization(self, mock_llm_engine):
        """Test tool orchestrator initializes correctly."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        assert orchestrator.llm_engine is not None
        assert orchestrator.test_mode is True
        assert orchestrator.get_tool_selection_count() == 0
        assert orchestrator.get_tool_execution_count() == 0

    def test_tool_selection_for_property_performance(self, mock_llm_engine):
        """Test tool selection for property performance intent."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        tools = orchestrator.select_tools(parsed_intent)
        
        assert "get_property_metrics" in tools
        assert orchestrator.get_tool_selection_count() == 1

    def test_tool_selection_low_confidence(self, mock_llm_engine):
        """Test tool selection with low confidence intent."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.3,  # Low confidence
            parameters={"property_ids": ["P123"]}
        )
        
        tools = orchestrator.select_tools(parsed_intent)
        
        assert tools == []  # Should not select tools with low confidence
        assert orchestrator.get_tool_selection_count() == 1

    def test_tool_execution_success(self, mock_llm_engine):
        """Test successful tool execution."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        # Mock tool function
        mock_tool = Mock(return_value={"occupancy_rate": 0.85, "revenue": 12500})
        available_tools = {"get_property_metrics": mock_tool}
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "time_period": "last_30_days"}
        )
        
        results = orchestrator.execute_tools(parsed_intent, available_tools)
        
        assert len(results) == 1
        assert results[0].success is True
        assert results[0].tool_name == "get_property_metrics"
        assert results[0].data["occupancy_rate"] == 0.85
        assert orchestrator.get_successful_executions() == 1
        assert orchestrator.get_failed_executions() == 0

    def test_tool_execution_failure(self, mock_llm_engine):
        """Test tool execution failure handling."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        # Mock tool function that raises exception
        mock_tool = Mock(side_effect=ValueError("Property not found"))
        available_tools = {"get_property_metrics": mock_tool}
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P999"]}
        )
        
        results = orchestrator.execute_tools(parsed_intent, available_tools)
        
        assert len(results) == 1
        assert results[0].success is False
        assert results[0].tool_name == "get_property_metrics"
        assert "Property not found" in results[0].error_message
        assert orchestrator.get_successful_executions() == 0
        assert orchestrator.get_failed_executions() == 1

    def test_parameter_extraction_for_metrics(self, mock_llm_engine):
        """Test parameter extraction for metrics tool."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "time_period": "last_30_days",
                "metrics": ["occupancy_rate", "revenue"]
            }
        )
        
        params = orchestrator._extract_parameters_for_tool("get_property_metrics", parsed_intent)
        
        assert params["property_id"] == "P123"
        assert params["time_period"] == "last_30_days"
        assert params["metrics"] == ["occupancy_rate", "revenue"]

    def test_parameter_extraction_for_comparison(self, mock_llm_engine):
        """Test parameter extraction for comparison tool."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={
                "property_ids": ["P456"],
                "comparison_type": "vs_cohort",
                "time_period": "last_quarter"
            }
        )
        
        params = orchestrator._extract_parameters_for_tool("get_property_comparisons", parsed_intent)
        
        assert params["property_id"] == "P456"
        assert params["comparison_type"] == "vs_cohort"
        assert params["time_period"] == "last_quarter"

    def test_success_rate_calculation(self, mock_llm_engine):
        """Test success rate calculation."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        
        # Mock tools - one success, one failure
        success_tool = Mock(return_value={"data": "success"})
        failure_tool = Mock(side_effect=Exception("Failed"))
        available_tools = {
            "success_tool": success_tool,
            "failure_tool": failure_tool
        }
        
        # Register tools for testing
        orchestrator.register_tool_for_intent(QueryIntent.GENERAL_INQUIRY, "success_tool")
        orchestrator.register_tool_for_intent(QueryIntent.GENERAL_INQUIRY, "failure_tool")
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.GENERAL_INQUIRY,
            confidence=0.9,
            parameters={}
        )
        
        results = orchestrator.execute_tools(parsed_intent, available_tools)
        
        assert len(results) == 2
        assert orchestrator.get_success_rate() == 0.5  # 1 success out of 2 executions

    def test_result_synthesizer_initialization(self, mock_llm_engine):
        """Test result synthesizer initializes correctly."""
        synthesizer = ResultSynthesizer(mock_llm_engine, test_mode=True)
        
        assert synthesizer.llm_engine is not None
        assert synthesizer.test_mode is True
        assert synthesizer.get_synthesis_count() == 0

    def test_synthesize_response_with_results(self, mock_llm_engine):
        """Test response synthesis with tool results."""
        # Mock LLM response
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Property P123 has an occupancy rate of 85% and generated $12,500 in revenue."
        })
        
        synthesizer = ResultSynthesizer(mock_llm_engine, test_mode=True)
        
        tool_results = [
            ToolResult(
                tool_name="get_property_metrics",
                success=True,
                data={"occupancy_rate": 0.85, "revenue": 12500},
                execution_time=0.5
            )
        ]
        
        response = synthesizer.synthesize_response(
            "How is P123 performing?",
            {"intent": "property_performance"},
            tool_results
        )
        
        assert "Property P123" in response
        assert "85%" in response
        assert "$12,500" in response
        assert synthesizer.get_synthesis_count() == 1

    def test_synthesize_fallback_response(self, mock_llm_engine):
        """Test fallback response generation."""
        synthesizer = ResultSynthesizer(mock_llm_engine, test_mode=True)
        
        response = synthesizer.synthesize_response(
            "How is P123 performing?",
            {"intent": "property_performance"},
            []  # No tool results
        )
        
        assert "more specific information" in response
        assert synthesizer.get_fallback_count() == 1

    def test_synthesize_error_response(self, mock_llm_engine):
        """Test error response generation."""
        # Mock LLM response for error handling
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "I apologize, but there was an issue accessing the property data. Please check the property ID and try again."
        })
        
        synthesizer = ResultSynthesizer(mock_llm_engine, test_mode=True)
        
        tool_results = [
            ToolResult(
                tool_name="get_property_metrics",
                success=False,
                data=None,
                error_message="Property not found",
                execution_time=0.1
            )
        ]
        
        response = synthesizer.synthesize_response(
            "How is P999 performing?",
            {"intent": "property_performance"},
            tool_results
        )
        
        assert "apologize" in response
        assert synthesizer.get_error_response_count() == 1

    def test_counter_reset(self, mock_llm_engine):
        """Test counter reset functionality."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)
        synthesizer = ResultSynthesizer(mock_llm_engine, test_mode=True)
        
        # Perform some operations
        parsed_intent = ParsedIntent(QueryIntent.PROPERTY_PERFORMANCE, 0.9, {})
        orchestrator.select_tools(parsed_intent)
        synthesizer.synthesize_response("test", {}, [])
        
        # Reset counters
        orchestrator.reset_counters()
        synthesizer.reset_counters()
        
        assert orchestrator.get_tool_selection_count() == 0
        assert synthesizer.get_synthesis_count() == 0
