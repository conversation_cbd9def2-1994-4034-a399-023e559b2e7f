"""
Unit tests for multi-step reasoning engine with mocked dependencies.
"""
import pytest
from unittest.mock import Mo<PERSON>, MagicMock

from pm_ai.intelligence.reasoning_engine import (
    ReasoningE<PERSON><PERSON>, Reasoning<PERSON>hain, ReasoningNode, ReasoningStep
)
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.unit
class TestReasoningEngineUnit:
    """Unit tests for reasoning engine with mocked dependencies."""

    def test_reasoning_engine_initialization(self, mock_llm_engine):
        """Test reasoning engine initializes correctly."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        assert engine.llm_engine is not None
        assert engine.test_mode is True
        assert engine.get_chains_executed() == 0
        assert len(engine.reasoning_patterns) > 0

    def test_create_reasoning_chain_property_performance(self, mock_llm_engine):
        """Test creating reasoning chain for property performance query."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "original_query": "How is P123 performing?"}
        )
        
        chain = engine.create_reasoning_chain(parsed_intent)
        
        assert chain.chain_id.startswith("chain_")
        assert chain.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert len(chain.nodes) == 3  # DATA_COLLECTION, ANALYSIS, SYNTHESIS
        assert chain.execution_order == [node.step_id for node in chain.nodes]
        assert engine.get_chains_executed() == 1

    def test_create_reasoning_chain_recommendations(self, mock_llm_engine):
        """Test creating reasoning chain for recommendations query."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P456"]}
        )
        
        chain = engine.create_reasoning_chain(parsed_intent)
        
        assert len(chain.nodes) == 5  # DATA_COLLECTION, ANALYSIS, INFERENCE, SYNTHESIS, VALIDATION
        
        # Check step types
        step_types = [node.step_type for node in chain.nodes]
        assert ReasoningStep.DATA_COLLECTION in step_types
        assert ReasoningStep.ANALYSIS in step_types
        assert ReasoningStep.INFERENCE in step_types
        assert ReasoningStep.SYNTHESIS in step_types
        assert ReasoningStep.VALIDATION in step_types

    def test_reasoning_node_creation(self, mock_llm_engine):
        """Test reasoning node creation with correct properties."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        node = engine._create_reasoning_node(
            "test_step", ReasoningStep.DATA_COLLECTION, parsed_intent
        )
        
        assert node.step_id == "test_step"
        assert node.step_type == ReasoningStep.DATA_COLLECTION
        assert "get_property_metrics" in node.tools_required
        assert not node.completed
        assert node.confidence == 0.0

    def test_node_dependencies_setting(self, mock_llm_engine):
        """Test that node dependencies are set correctly."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        chain = engine.create_reasoning_chain(parsed_intent)
        
        # First node should have no dependencies
        assert len(chain.nodes[0].dependencies) == 0
        
        # Second node should depend on first
        assert chain.nodes[0].step_id in chain.nodes[1].dependencies
        
        # Synthesis node should depend on analysis steps
        synthesis_node = next(node for node in chain.nodes if node.step_type == ReasoningStep.SYNTHESIS)
        assert len(synthesis_node.dependencies) > 1

    def test_execute_reasoning_chain_success(self, mock_llm_engine):
        """Test successful execution of reasoning chain."""
        # Mock LLM responses
        mock_llm_engine.create_completion = Mock(return_value={
            "content": "Analysis complete with insights."
        })
        
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Create simple chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        chain = engine.create_reasoning_chain(parsed_intent)
        
        # Mock tools
        mock_tool = Mock(return_value={"occupancy_rate": 85, "revenue": 12000})
        available_tools = {"get_property_metrics": mock_tool}
        
        # Execute chain
        result_chain = engine.execute_reasoning_chain(chain, available_tools)
        
        assert result_chain.completed is True
        assert result_chain.confidence > 0
        assert result_chain.final_result is not None
        assert engine.get_successful_chains() == 1

    def test_execute_reasoning_chain_failure(self, mock_llm_engine):
        """Test handling of reasoning chain execution failure."""
        # Mock LLM to raise exception
        mock_llm_engine.create_completion = Mock(side_effect=Exception("LLM Error"))
        
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        chain = engine.create_reasoning_chain(parsed_intent)
        
        # Mock tools
        mock_tool = Mock(return_value={"occupancy_rate": 85})
        available_tools = {"get_property_metrics": mock_tool}
        
        # Execute chain
        result_chain = engine.execute_reasoning_chain(chain, available_tools)
        
        assert "error" in result_chain.final_result
        assert result_chain.confidence <= 0.3  # Should be low confidence due to failures
        assert engine.get_failed_chains() == 0  # Chain completed but with errors

    def test_data_collection_execution(self, mock_llm_engine):
        """Test data collection step execution."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Create data collection node
        node = ReasoningNode(
            step_id="data_step",
            step_type=ReasoningStep.DATA_COLLECTION,
            description="Collect data",
            tools_required=["get_property_metrics"],
            data_inputs={"parameters": {"property_ids": ["P123"]}}
        )
        
        # Mock tool
        mock_tool = Mock(return_value={"occupancy_rate": 85, "revenue": 12000})
        available_tools = {"get_property_metrics": mock_tool}
        
        # Execute data collection
        engine._execute_data_collection(node, available_tools)
        
        assert "collected_data" in node.data_outputs
        assert "get_property_metrics" in node.data_outputs["collected_data"]
        assert node.data_outputs["collected_data"]["get_property_metrics"]["occupancy_rate"] == 85

    def test_tool_parameter_extraction(self, mock_llm_engine):
        """Test extraction of tool parameters from data inputs."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        data_inputs = {
            "parameters": {
                "property_ids": ["P123"],
                "time_period": "last_30_days",
                "metrics": ["occupancy_rate", "revenue"]
            }
        }
        
        params = engine._extract_tool_parameters("get_property_metrics", data_inputs)
        
        assert params["property_id"] == "P123"
        assert params["time_period"] == "last_30_days"
        assert params["metrics"] == ["occupancy_rate", "revenue"]

    def test_gather_previous_data(self, mock_llm_engine):
        """Test gathering data from previous reasoning steps."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Create chain with completed nodes
        chain = ReasoningChain("test_chain", "test query", QueryIntent.PROPERTY_PERFORMANCE)
        
        node1 = ReasoningNode("step1", ReasoningStep.DATA_COLLECTION, "Collect data")
        node1.completed = True
        node1.data_outputs = {"data": "collected"}
        
        node2 = ReasoningNode("step2", ReasoningStep.ANALYSIS, "Analyze data")
        node2.completed = True
        node2.data_outputs = {"analysis": "complete"}
        
        node3 = ReasoningNode("step3", ReasoningStep.SYNTHESIS, "Synthesize")
        
        chain.nodes = [node1, node2, node3]
        
        # Gather data for synthesis step
        gathered = engine._gather_previous_data(chain, node3)
        
        assert "step1" in gathered
        assert "step2" in gathered
        assert gathered["step1"]["data"] == "collected"
        assert gathered["step2"]["analysis"] == "complete"

    def test_chain_confidence_calculation(self, mock_llm_engine):
        """Test calculation of reasoning chain confidence."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Create chain with mixed completion
        chain = ReasoningChain("test_chain", "test query", QueryIntent.PROPERTY_PERFORMANCE)
        
        node1 = ReasoningNode("step1", ReasoningStep.DATA_COLLECTION, "Collect")
        node1.completed = True
        node1.confidence = 0.9
        
        node2 = ReasoningNode("step2", ReasoningStep.ANALYSIS, "Analyze")
        node2.completed = True
        node2.confidence = 0.8
        
        node3 = ReasoningNode("step3", ReasoningStep.SYNTHESIS, "Synthesize")
        node3.completed = False
        node3.confidence = 0.0
        
        chain.nodes = [node1, node2, node3]
        
        confidence = engine._calculate_chain_confidence(chain)
        
        # Should be (0.9 + 0.8) / 2 * (2/3) = 0.85 * 0.667 ≈ 0.567
        assert 0.5 < confidence < 0.6

    def test_success_rate_calculation(self, mock_llm_engine):
        """Test success rate calculation."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Simulate some executions
        engine.chains_executed = 10
        engine.successful_chains = 7
        engine.failed_chains = 3
        
        assert engine.get_success_rate() == 0.7
        
        # Test with no executions
        engine.reset_metrics()
        assert engine.get_success_rate() == 0.0

    def test_metrics_reset(self, mock_llm_engine):
        """Test metrics reset functionality."""
        engine = ReasoningEngine(mock_llm_engine, test_mode=True)
        
        # Set some metrics
        engine.chains_executed = 5
        engine.successful_chains = 3
        engine.failed_chains = 2
        
        # Reset
        engine.reset_metrics()
        
        assert engine.get_chains_executed() == 0
        assert engine.get_successful_chains() == 0
        assert engine.get_failed_chains() == 0
