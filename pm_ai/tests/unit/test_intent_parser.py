"""
Unit tests for intent parser with mocked LLM responses.
"""
import pytest
from unittest.mock import Mock, patch
import json

from pm_ai.intelligence.intent_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryIntent, ParsedIntent

@pytest.mark.unit
class TestIntentParserUnit:
    """Unit tests for intent parser with mocked dependencies."""

    def test_initialization(self, mock_llm_engine):
        """Test intent parser initializes correctly."""
        parser = IntentParser(mock_llm_engine)
        assert parser.llm_engine is not None
        assert parser.system_prompt is not None
        assert parser.get_parse_count() == 0

    def test_parse_property_performance_query(self, mock_llm_engine, mock_intent_response):
        """Test parsing property performance query with mocked response."""
        parser = IntentParser(mock_llm_engine)
        
        # Mock the LLM engine to return our test response
        def mock_create_completion(*args, **kwargs):
            return {
                "content": None,
                "function_call": {
                    "name": "extract_intent",
                    "arguments": mock_intent_response
                },
                "finish_reason": "function_call",
                "usage": {"total_tokens": 50}
            }

        parser.llm_engine.create_completion = mock_create_completion

        result = parser.parse_query("How is property P123 performing this month?")

        assert result.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert result.confidence == 0.95
        assert "P123" in result.parameters["property_ids"]
        assert result.parameters["time_period"] == "last_30_days"
        assert parser.get_parse_count() == 1

    def test_parse_empty_query(self, mock_llm_engine):
        """Test handling of empty queries."""
        parser = IntentParser(mock_llm_engine)
        result = parser.parse_query("")

        assert result.intent == QueryIntent.CLARIFICATION_NEEDED
        assert result.confidence == 0.0
        assert result.clarification_needed is not None

    def test_parse_error_handling(self, mock_llm_engine):
        """Test error handling when LLM fails."""
        parser = IntentParser(mock_llm_engine)
        
        # Configure mock to raise exception
        parser.llm_engine.create_completion = Mock(side_effect=Exception("API Error"))

        result = parser.parse_query("Test query")

        assert result.intent == QueryIntent.CLARIFICATION_NEEDED
        assert result.confidence == 0.0
        assert "error" in result.parameters

    def test_fallback_intent_creation(self, mock_llm_engine):
        """Test fallback intent when function calling fails."""
        parser = IntentParser(mock_llm_engine)
        
        # Configure mock to return no function call
        def mock_create_completion(*args, **kwargs):
            return {
                "content": "I couldn't parse that",
                "function_call": None,
                "finish_reason": "stop",
                "usage": {"total_tokens": 20}
            }

        parser.llm_engine.create_completion = mock_create_completion

        result = parser.parse_query("Unclear query")

        assert result.intent == QueryIntent.GENERAL_INQUIRY
        assert result.confidence == 0.3
        assert result.clarification_needed is not None

    def test_various_intent_types(self, mock_llm_engine):
        """Test parsing different types of intents."""
        parser = IntentParser(mock_llm_engine)
        
        test_cases = [
            ("Compare P123 to similar properties", QueryIntent.PROPERTY_COMPARISON),
            ("What recommendations do you have for P456?", QueryIntent.RECOMMENDATIONS),
            ("Show me outliers for P789", QueryIntent.OUTLIER_ANALYSIS),
            ("List downtown properties", QueryIntent.COHORT_ANALYSIS)
        ]

        for query, expected_intent in test_cases:
            # Mock response for each intent type
            def mock_create_completion(*args, **kwargs):
                return {
                    "content": None,
                    "function_call": {
                        "name": "extract_intent",
                        "arguments": {
                            "intent": expected_intent.value,
                            "confidence": 0.9,
                            "parameters": {"test": "data"}
                        }
                    },
                    "finish_reason": "function_call",
                    "usage": {"total_tokens": 30}
                }

            parser.llm_engine.create_completion = mock_create_completion
            result = parser.parse_query(query)

            assert result.intent == expected_intent
            assert result.confidence == 0.9

    def test_parse_count_tracking(self, mock_llm_engine):
        """Test parse count tracking for testing validation."""
        parser = IntentParser(mock_llm_engine)
        
        # Mock successful response
        def mock_create_completion(*args, **kwargs):
            return {
                "content": None,
                "function_call": {
                    "name": "extract_intent",
                    "arguments": {
                        "intent": "general_inquiry",
                        "confidence": 0.8,
                        "parameters": {}
                    }
                },
                "finish_reason": "function_call",
                "usage": {"total_tokens": 25}
            }

        parser.llm_engine.create_completion = mock_create_completion

        # Parse multiple queries
        parser.parse_query("Query 1")
        parser.parse_query("Query 2")
        parser.parse_query("Query 3")

        assert parser.get_parse_count() == 3

    def test_context_handling(self, mock_llm_engine):
        """Test parsing with conversation context."""
        parser = IntentParser(mock_llm_engine)
        
        context = {
            "previous_property": "P123",
            "last_query_type": "property_performance"
        }

        # Mock response
        def mock_create_completion(*args, **kwargs):
            return {
                "content": None,
                "function_call": {
                    "name": "extract_intent",
                    "arguments": {
                        "intent": "property_performance",
                        "confidence": 0.9,
                        "parameters": {"property_ids": ["P123"]}
                    }
                },
                "finish_reason": "function_call",
                "usage": {"total_tokens": 40}
            }

        parser.llm_engine.create_completion = mock_create_completion

        result = parser.parse_query("How is it doing now?", context)

        assert result.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert result.confidence == 0.9
