"""
Integration tests for conversation flow with real conversation management.
"""
import pytest
from datetime import datetime

from pm_ai.conversation.memory_store import MemoryS<PERSON>
from pm_ai.conversation.session_manager import Session<PERSON>anager
from pm_ai.intelligence.context_manager import ContextManager

@pytest.mark.unit
class TestConversationFlowIntegration:
    """Integration tests for conversation flow management."""

    def test_complete_conversation_flow(self):
        """Test complete conversation flow from start to finish."""
        # Create test-aware components
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)

        # Start conversation
        session_id = session_manager.start_session("test_user")
        assert session_id is not None
        assert session_manager.get_active_session_id() == session_id

        # First interaction
        turn1_id = session_manager.add_interaction(
            "How is property P123 performing?",
            {"intent": "property_performance", "parameters": {"property_ids": ["P123"]}},
            "Property P123 has 85% occupancy rate and $150 ADR.",
            ["get_property_metrics"]
        )
        assert turn1_id is not None
        assert session_manager.get_interaction_count() == 1

        # Second interaction with reference
        enriched = context_manager.enrich_query("What about compared to last year?")
        assert enriched["original_query"] == "What about compared to last year?"
        assert len(enriched["context"]["history"]) == 1

        turn2_id = session_manager.add_interaction(
            "What about compared to last year?",
            {"intent": "property_comparison", "parameters": {"property_ids": ["P123"], "comparison_type": "vs_previous_period"}},
            "P123 improved by 10% compared to last year.",
            ["get_property_comparisons"]
        )
        assert turn2_id is not None
        assert session_manager.get_interaction_count() == 2

        # Verify conversation context
        context = session_manager.build_conversation_context()
        assert len(context["history"]) == 2
        assert "P123" in context["references"]["properties"]

    def test_session_timeout_and_renewal(self):
        """Test session timeout and automatic renewal."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)

        # Create initial session
        session_id1 = session_manager.start_session()
        
        # Simulate session timeout by manually setting last activity
        session = memory_store.get_session(session_id1)
        session.last_activity = datetime.now() - session_manager.session_timeout * 2

        # Try to get session - should create new one due to timeout
        session_id2 = session_manager.get_or_create_session(session_id1)
        assert session_id2 != session_id1  # Should be different due to timeout

    def test_memory_store_limits(self):
        """Test memory store session and turn limits."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)

        # Create sessions up to limit
        session_ids = []
        for i in range(memory_store.max_sessions + 5):  # Exceed limit
            session_id = session_manager.start_session(f"user_{i}")
            session_ids.append(session_id)

        # Should have triggered cleanup
        assert memory_store.get_cleanup_count() > 0
        assert memory_store.get_session_count() <= memory_store.max_sessions

    def test_turn_limit_per_session(self):
        """Test turn limit per session."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)

        session_id = session_manager.start_session()

        # Add turns up to limit
        for i in range(memory_store.max_turns_per_session + 3):  # Exceed limit
            session_manager.add_interaction(
                f"Query {i}",
                {"intent": "general_inquiry", "parameters": {}},
                f"Response {i}",
                []
            )

        # Should have limited turns
        assert session_manager.get_session_turn_count() <= memory_store.max_turns_per_session

    def test_context_reference_extraction(self):
        """Test reference extraction from conversation history."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)

        session_id = session_manager.start_session()

        # Add interactions with various entities
        session_manager.add_interaction(
            "How are P123 and P456 in the beachfront cohort doing?",
            {
                "intent": "cohort_analysis",
                "parameters": {
                    "property_ids": ["P123", "P456"],
                    "cohort": "beachfront",
                    "metrics": ["occupancy_rate", "revenue"]
                }
            },
            "The beachfront properties are performing well.",
            ["get_cohort_metrics"]
        )

        # Build context and verify references
        context = session_manager.build_conversation_context()
        references = context["references"]

        assert "P123" in references["properties"]
        assert "P456" in references["properties"]
        assert "beachfront" in references["cohorts"]
        assert "occupancy_rate" in references["metrics"]
        assert "revenue" in references["metrics"]

    def test_context_manager_reference_resolution(self):
        """Test context manager reference resolution in conversation."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)

        # Start conversation about specific property
        session_id = session_manager.start_session()
        session_manager.add_interaction(
            "Show me P789 performance",
            {"intent": "property_performance", "parameters": {"property_ids": ["P789"]}},
            "P789 has excellent performance metrics.",
            ["get_property_metrics"]
        )

        # Ask follow-up with pronoun
        enriched = context_manager.enrich_query("How is it trending?")
        
        assert "property P789" in enriched["enriched_query"]
        assert context_manager.get_resolution_count() == 1

    @pytest.mark.slow
    def test_large_conversation_performance(self):
        """Test performance with larger conversation history (marked as slow)."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)

        session_id = session_manager.start_session()

        # Add many interactions
        for i in range(50):
            session_manager.add_interaction(
                f"Query about P{i:03d}",
                {"intent": "property_performance", "parameters": {"property_ids": [f"P{i:03d}"]}},
                f"Response about P{i:03d}",
                ["get_property_metrics"]
            )

        # Should still work efficiently
        context = session_manager.build_conversation_context()
        assert len(context["history"]) <= 5  # Should limit to recent turns

        # Context enrichment should still work
        enriched = context_manager.enrich_query("What about that property?")
        assert enriched is not None

    def test_operation_counting(self):
        """Test operation counting for performance monitoring."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)

        # Perform various operations
        session_id = session_manager.start_session()
        session_manager.add_interaction("Query 1", {}, "Response 1", [])
        context_manager.enrich_query("Query 2")
        
        # Verify counts
        assert memory_store.get_operation_count() > 0
        assert session_manager.get_interaction_count() == 1
        assert context_manager.get_enrichment_count() == 1
