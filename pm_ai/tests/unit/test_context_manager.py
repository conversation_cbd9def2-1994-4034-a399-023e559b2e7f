"""
Unit tests for context manager with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from pm_ai.intelligence.context_manager import ContextManager
from pm_ai.conversation.session_manager import SessionManager
from pm_ai.conversation.memory_store import MemoryStore, ConversationSession, ConversationTurn

@pytest.mark.unit
class TestContextManagerUnit:
    """Unit tests for context manager with mocked dependencies."""

    def test_initialization(self):
        """Test context manager initializes correctly."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        assert context_manager.session_manager is not None
        assert context_manager.test_mode is True
        assert context_manager.get_enrichment_count() == 0
        assert context_manager.get_resolution_count() == 0

    def test_enrich_query_no_context(self):
        """Test query enrichment with no conversation context."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        result = context_manager.enrich_query("How is P123 performing?")
        
        assert result["original_query"] == "How is P123 performing?"
        assert result["enriched_query"] == "How is P123 performing?"  # No changes without context
        assert result["context"]["history"] == []
        assert context_manager.get_enrichment_count() == 1

    def test_enrich_query_with_context(self):
        """Test query enrichment with conversation context."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Create a session with some history
        session_id = session_manager.start_session()
        
        # Add a previous interaction about P123
        session_manager.add_interaction(
            "How is P123 performing?",
            {"intent": "property_performance", "parameters": {"property_ids": ["P123"]}},
            "P123 is performing well with 85% occupancy.",
            ["get_property_metrics"]
        )
        
        # Now ask a follow-up question with pronoun
        result = context_manager.enrich_query("How is it doing compared to last year?")
        
        assert result["original_query"] == "How is it doing compared to last year?"
        assert "property P123" in result["enriched_query"]  # Should resolve "it" to "property P123"
        assert len(result["context"]["history"]) == 1
        assert context_manager.get_enrichment_count() == 1
        assert context_manager.get_resolution_count() == 1

    def test_reference_resolution_property(self):
        """Test property reference resolution."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Mock context with property references
        context = {
            "history": [{"user": "How is P456 doing?", "system": "P456 is great"}],
            "references": {
                "properties": {"P456": "2024-01-01T10:00:00"},
                "cohorts": [],
                "time_periods": {},
                "metrics": []
            }
        }
        
        enriched = context_manager._resolve_references("What about that property?", context)
        assert "property P456" in enriched

    def test_reference_resolution_time_period(self):
        """Test time period reference resolution."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Mock context with time period references
        context = {
            "history": [{"user": "Show me last month data", "system": "Here's the data"}],
            "references": {
                "properties": {},
                "cohorts": [],
                "time_periods": {"last_month": "2024-01-01T10:00:00"},
                "metrics": []
            }
        }
        
        enriched = context_manager._resolve_references("What about that period?", context)
        assert "last_month" in enriched

    def test_update_context_after_response(self):
        """Test context update after response generation."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Start a session
        session_id = session_manager.start_session()
        
        # Update context after response
        parsed_intent = {"intent": "property_performance", "parameters": {"property_ids": ["P789"]}}
        response_content = "Property P789 has excellent occupancy rates."
        tools_used = ["get_property_metrics"]
        
        context_manager.update_context_after_response(parsed_intent, response_content, tools_used)
        
        # Verify context was updated
        context = session_manager.build_conversation_context()
        assert "last_entities" in context
        assert "last_intent" in context
        assert "last_tools" in context

    def test_entity_extraction_from_response(self):
        """Test entity extraction from system response."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        response = "Property P123 and P456 in the beachfront cohort have high occupancy rates and good revenue."
        entities = context_manager._extract_entities_from_response(response)
        
        assert "P123" in entities["properties"]
        assert "P456" in entities["properties"]
        assert "beachfront" in entities["cohorts"]
        assert "occupancy_rate" in entities["metrics"] or "occupancy" in entities["metrics"]
        assert "revenue" in entities["metrics"]

    def test_counter_tracking(self):
        """Test counter tracking for testing validation."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Perform multiple operations
        context_manager.enrich_query("Query 1")
        context_manager.enrich_query("Query 2")
        
        assert context_manager.get_enrichment_count() == 2
        
        # Reset counters
        context_manager.reset_counters()
        assert context_manager.get_enrichment_count() == 0
        assert context_manager.get_resolution_count() == 0

    def test_no_reference_resolution_without_history(self):
        """Test that reference resolution is skipped without history."""
        memory_store = MemoryStore(test_mode=True)
        session_manager = SessionManager(memory_store, test_mode=True)
        context_manager = ContextManager(session_manager, test_mode=True)
        
        # Empty context
        context = {"history": [], "references": {}}
        
        query = "How is it performing?"
        enriched = context_manager._resolve_references(query, context)
        
        # Should return unchanged
        assert enriched == query
        assert context_manager.get_resolution_count() == 0
