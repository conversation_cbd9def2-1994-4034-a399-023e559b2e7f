"""
Unit tests for Phase 2 advanced agent with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, MagicMock

from pm_ai.agents.phase2_agent import Phase2Agent

@pytest.mark.unit
class TestPhase2AgentUnit:
    """Unit tests for Phase 2 agent with mocked dependencies."""

    def test_phase2_agent_initialization(self):
        """Test Phase 2 agent initializes correctly."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("phase2_agent", "Advanced property management assistant", tools, test_mode=True)
        
        assert agent.name == "phase2_agent"
        assert agent.instructions == "Advanced property management assistant"
        assert agent.test_mode is True
        assert len(agent.tools) == 1
        assert len(agent.available_tools) == 1
        
        # Verify all intelligence modules are initialized
        assert agent.llm_engine is not None
        assert agent.intent_parser is not None
        assert agent.reasoning_engine is not None
        assert agent.query_planner is not None
        assert agent.advanced_synthesizer is not None
        assert agent.error_handler is not None
        assert agent.context_manager is not None
        assert agent.session_manager is not None
        assert agent.tool_orchestrator is not None

    def test_agent_component_integration(self):
        """Test that all agent components are properly integrated."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]

        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)

        # Verify all components are initialized
        assert agent.llm_engine is not None
        assert agent.intent_parser is not None
        assert agent.reasoning_engine is not None
        assert agent.query_planner is not None
        assert agent.advanced_synthesizer is not None
        assert agent.error_handler is not None
        assert agent.context_manager is not None
        assert agent.session_manager is not None
        assert agent.tool_orchestrator is not None

        # Verify tool mapping works
        assert "get_property_metrics" in agent.available_tools
        assert len(agent.available_tools) == 1

        # Verify agent properties
        assert agent.name == "test_agent"
        assert agent.test_mode is True

    def test_complex_query_identification(self):
        """Test identification of complex queries requiring advanced reasoning."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]

        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)

        # Test complex query identification
        complex_intent = Mock()
        complex_intent.intent.value = "recommendations"
        complex_intent.confidence = 0.8
        complex_intent.parameters = {"property_ids": ["P123", "P456"]}

        # Should require advanced reasoning due to multiple properties + recommendations intent
        assert agent._requires_advanced_reasoning(complex_intent, "Give me comprehensive analysis")

        # Test synthesis mode determination
        assert agent._determine_synthesis_mode(complex_intent, "Give me recommendations").value == "analytical"

    def test_complexity_determination(self):
        """Test complexity determination logic."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)
        
        # Simple query
        simple_intent = Mock(
            intent=Mock(value="property_performance"),
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        assert not agent._requires_advanced_reasoning(simple_intent, "How is P123?")
        
        # Complex query - multiple properties
        complex_intent1 = Mock(
            intent=Mock(value="property_performance"),
            confidence=0.9,
            parameters={"property_ids": ["P123", "P456", "P789"]}
        )
        assert agent._requires_advanced_reasoning(complex_intent1, "How are my properties?")
        
        # Complex query - recommendations intent
        complex_intent2 = Mock(
            intent=Mock(value="recommendations"),
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        assert agent._requires_advanced_reasoning(complex_intent2, "What recommendations do you have?")
        
        # Complex query - complex language + low confidence
        simple_intent_complex_lang = Mock(
            intent=Mock(value="property_performance"),
            confidence=0.6,  # Low confidence + complex keywords = 2 indicators
            parameters={"property_ids": ["P123"]}
        )
        assert agent._requires_advanced_reasoning(simple_intent_complex_lang, "Give me a comprehensive detailed analysis")

    def test_synthesis_mode_determination(self):
        """Test synthesis mode determination."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)
        
        # Executive mode
        exec_intent = Mock(intent=Mock(value="property_performance"))
        assert agent._determine_synthesis_mode(exec_intent, "Give me an executive summary").value == "executive"
        
        # Technical mode
        tech_intent = Mock(intent=Mock(value="property_performance"))
        assert agent._determine_synthesis_mode(tech_intent, "Provide technical analysis").value == "technical"
        
        # Narrative mode
        narrative_intent = Mock(intent=Mock(value="property_performance"))
        assert agent._determine_synthesis_mode(narrative_intent, "Tell me the story").value == "narrative"
        
        # Analytical mode for recommendations
        rec_intent = Mock(intent=Mock(value="recommendations"))
        assert agent._determine_synthesis_mode(rec_intent, "What do you recommend?").value == "analytical"

    def test_error_handling_processing_error(self):
        """Test error handling for general processing errors."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)
        
        # Mock error handler
        mock_recovery_plan = Mock()
        mock_recovery_plan.clarification_message = "I encountered an error. Please try again."
        mock_recovery_plan.strategy = Mock(value="fallback_response")
        mock_recovery_plan.confidence = 0.3
        
        agent.error_handler.handle_error = Mock(return_value=mock_recovery_plan)
        agent.session_manager.get_conversation_context = Mock(return_value={"history": []})
        
        # Simulate processing error
        agent.context_manager.enrich_query = Mock(side_effect=Exception("Processing failed"))
        
        result = agent.process_query("Test query", "user123")
        
        assert result["error"] is True
        assert result["response"] == "I encountered an error. Please try again."
        assert result["recovery_strategy"] == "fallback_response"
        assert result["confidence"] == 0.3

    def test_error_handling_components_integration(self):
        """Test that error handling components are properly integrated."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]

        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)

        # Verify error handler is initialized
        assert agent.error_handler is not None

        # Test error context creation methods exist
        assert hasattr(agent, '_handle_processing_error')
        assert hasattr(agent, '_handle_reasoning_error')
        assert hasattr(agent, '_handle_tool_error')

    def test_agent_status_reporting(self):
        """Test agent status and metrics reporting."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)
        
        # Mock metrics
        agent.reasoning_engine.get_chains_executed = Mock(return_value=5)
        agent.query_planner.get_plans_created = Mock(return_value=3)
        agent.advanced_synthesizer.get_synthesis_count = Mock(return_value=8)
        agent.error_handler.get_errors_handled = Mock(return_value=2)
        agent.session_manager.active_sessions = {"user1": {}, "user2": {}}
        
        status = agent.get_agent_status()
        
        assert status["name"] == "test_agent"
        assert status["phase"] == "2"
        assert "multi_step_reasoning" in status["capabilities"]
        assert "autonomous_query_planning" in status["capabilities"]
        assert "advanced_synthesis" in status["capabilities"]
        assert "context_aware_error_handling" in status["capabilities"]
        assert status["tools_available"] == 1
        assert status["metrics"]["reasoning_chains_executed"] == 5
        assert status["metrics"]["query_plans_created"] == 3
        assert status["metrics"]["synthesis_operations"] == 8
        assert status["metrics"]["errors_handled"] == 2
        assert status["metrics"]["conversations_active"] == 2

    def test_metrics_reset(self):
        """Test metrics reset functionality."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)
        
        # Mock reset methods
        agent.reasoning_engine.reset_metrics = Mock()
        agent.query_planner.reset_metrics = Mock()
        agent.advanced_synthesizer.reset_metrics = Mock()
        agent.error_handler.reset_metrics = Mock()
        agent.context_manager.reset_counters = Mock()
        agent.session_manager.reset_counters = Mock()
        agent.tool_orchestrator.reset_counters = Mock()
        
        agent.reset_metrics()
        
        # Verify all reset methods were called
        agent.reasoning_engine.reset_metrics.assert_called_once()
        agent.query_planner.reset_metrics.assert_called_once()
        agent.advanced_synthesizer.reset_metrics.assert_called_once()
        agent.error_handler.reset_metrics.assert_called_once()
        agent.context_manager.reset_counters.assert_called_once()
        agent.session_manager.reset_counters.assert_called_once()
        agent.tool_orchestrator.reset_counters.assert_called_once()

    def test_conversation_context_integration(self):
        """Test integration with conversation context management."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]

        agent = Phase2Agent("test_agent", "Test assistant", tools, test_mode=True)

        # Verify conversation components are properly integrated
        assert agent.session_manager is not None
        assert agent.context_manager is not None

        # Test that context manager has session manager
        assert agent.context_manager.session_manager is agent.session_manager

        # Test context enrichment method exists
        assert hasattr(agent.context_manager, 'enrich_query')
        assert hasattr(agent.session_manager, 'build_conversation_context')
