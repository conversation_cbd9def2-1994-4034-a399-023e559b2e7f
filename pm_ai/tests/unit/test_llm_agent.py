"""
Unit tests for LLM-powered agent with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, MagicMock

from pm_ai.agents import Agent, Runner
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.unit
class TestLLMAgentUnit:
    """Unit tests for LLM-powered agent with mocked dependencies."""

    def test_agent_initialization(self):
        """Test LLM-powered agent initializes correctly."""
        # Mock tools
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        assert agent.name == "test_agent"
        assert agent.instructions == "Test instructions"
        assert agent.test_mode is True
        assert len(agent.tools) == 1
        assert agent.get_tool_by_name("get_property_metrics") is not None

    def test_agent_intelligence_modules_initialized(self):
        """Test that all intelligence modules are initialized."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        assert agent.llm_engine is not None
        assert agent.intent_parser is not None
        assert agent.context_manager is not None
        assert agent.session_manager is not None
        assert agent.tool_orchestrator is not None
        assert agent.result_synthesizer is not None

    def test_runner_successful_flow(self):
        """Test successful query processing flow."""
        # Mock tools
        mock_tool = Mock(__name__="get_property_metrics")
        mock_tool.return_value = {"occupancy_rate": 85, "revenue": 12500}
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock the intelligence modules to return predictable results
        agent.context_manager.enrich_query = Mock(return_value={
            "enriched_query": "How is property P123 performing?",
            "context": {"history": []}
        })
        
        agent.intent_parser.parse_query = Mock(return_value=ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        ))
        
        agent.tool_orchestrator.execute_tools = Mock(return_value=[
            Mock(tool_name="get_property_metrics", success=True, data={"occupancy_rate": 85})
        ])
        
        agent.result_synthesizer.synthesize_response = Mock(return_value="Property P123 has 85% occupancy rate.")
        
        agent.session_manager.add_interaction = Mock()
        
        # Execute query
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert result["final_output"] == "Property P123 has 85% occupancy rate."
        
        # Verify all modules were called
        agent.context_manager.enrich_query.assert_called_once()
        agent.intent_parser.parse_query.assert_called_once()
        agent.tool_orchestrator.execute_tools.assert_called_once()
        agent.result_synthesizer.synthesize_response.assert_called_once()
        agent.session_manager.add_interaction.assert_called_once()

    def test_runner_error_handling(self):
        """Test error handling in query processing."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock context manager to raise exception
        agent.context_manager.enrich_query = Mock(side_effect=Exception("Context error"))
        agent.session_manager.add_interaction = Mock()
        
        # Execute query
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert "error" in result["final_output"].lower()
        assert "Context error" in result["final_output"]

    def test_runner_intent_parsing_error(self):
        """Test error handling when intent parsing fails."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock successful context enrichment but failing intent parsing
        agent.context_manager.enrich_query = Mock(return_value={
            "enriched_query": "How is property P123 performing?",
            "context": {"history": []}
        })
        
        agent.intent_parser.parse_query = Mock(side_effect=Exception("Intent parsing failed"))
        agent.session_manager.add_interaction = Mock()
        
        # Execute query
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert "error" in result["final_output"].lower()
        assert "Intent parsing failed" in result["final_output"]

    def test_runner_tool_execution_error(self):
        """Test error handling when tool execution fails."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock successful context and intent parsing but failing tool execution
        agent.context_manager.enrich_query = Mock(return_value={
            "enriched_query": "How is property P123 performing?",
            "context": {"history": []}
        })
        
        agent.intent_parser.parse_query = Mock(return_value=ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        ))
        
        agent.tool_orchestrator.execute_tools = Mock(side_effect=Exception("Tool execution failed"))
        agent.session_manager.add_interaction = Mock()
        
        # Execute query
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert "error" in result["final_output"].lower()
        assert "Tool execution failed" in result["final_output"]

    def test_runner_response_synthesis_error(self):
        """Test error handling when response synthesis fails."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock successful flow until response synthesis
        agent.context_manager.enrich_query = Mock(return_value={
            "enriched_query": "How is property P123 performing?",
            "context": {"history": []}
        })
        
        agent.intent_parser.parse_query = Mock(return_value=ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        ))
        
        agent.tool_orchestrator.execute_tools = Mock(return_value=[
            Mock(tool_name="get_property_metrics", success=True, data={"occupancy_rate": 85})
        ])
        
        agent.result_synthesizer.synthesize_response = Mock(side_effect=Exception("Synthesis failed"))
        agent.session_manager.add_interaction = Mock()
        
        # Execute query
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert "error" in result["final_output"].lower()
        assert "Synthesis failed" in result["final_output"]

    def test_agent_tool_mapping(self):
        """Test agent tool mapping functionality."""
        mock_tool1 = Mock(__name__="get_property_metrics")
        mock_tool2 = Mock(__name__="get_property_comparisons")
        tools = [mock_tool1, mock_tool2]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        assert agent.get_tool_by_name("get_property_metrics") == mock_tool1
        assert agent.get_tool_by_name("get_property_comparisons") == mock_tool2
        assert agent.get_tool_by_name("nonexistent_tool") is None

    def test_agent_available_tools_mapping(self):
        """Test that available_tools mapping is created correctly."""
        mock_tool1 = Mock(__name__="get_property_metrics")
        mock_tool2 = Mock(__name__="get_property_comparisons")
        tools = [mock_tool1, mock_tool2]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        assert "get_property_metrics" in agent.available_tools
        assert "get_property_comparisons" in agent.available_tools
        assert agent.available_tools["get_property_metrics"] == mock_tool1
        assert agent.available_tools["get_property_comparisons"] == mock_tool2

    def test_runner_context_update_error_handling(self):
        """Test that context update errors don't compound original errors."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        agent = Agent("test_agent", "Test instructions", tools, test_mode=True)
        
        # Mock context manager to raise exception
        agent.context_manager.enrich_query = Mock(side_effect=Exception("Original error"))
        
        # Mock session manager to also raise exception during error handling
        agent.session_manager.add_interaction = Mock(side_effect=Exception("Context update error"))
        
        # Execute query - should handle both errors gracefully
        result = Runner.run_sync(agent, "How is P123 performing?")
        
        assert "error" in result["final_output"].lower()
        assert "Original error" in result["final_output"]
        # Should not contain the context update error

    def test_agent_test_mode_logging(self):
        """Test that test mode enables appropriate logging."""
        mock_tool = Mock(__name__="get_property_metrics")
        tools = [mock_tool]
        
        # Test with test_mode=True
        agent_test = Agent("test_agent", "Test instructions", tools, test_mode=True)
        assert agent_test.test_mode is True
        
        # Test with test_mode=False
        agent_prod = Agent("prod_agent", "Prod instructions", tools, test_mode=False)
        assert agent_prod.test_mode is False
