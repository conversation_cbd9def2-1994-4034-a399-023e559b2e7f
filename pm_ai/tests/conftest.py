"""
Pytest configuration and fixtures for hybrid testing strategy.
Prevents API key handling conflicts with three-tier testing architecture.
"""
import pytest
import os
import sys
from unittest.mock import Mock
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(project_root))

# Import test configuration
from pm_ai.config.test_config import TestConfig

@pytest.fixture(scope="session")
def test_mode():
    """Determine test mode for the session."""
    return TestConfig.get_test_mode()

@pytest.fixture(scope="session")
def load_test_env():
    """Load test environment variables."""
    from dotenv import load_dotenv

    # Load test environment if it exists
    test_env_path = os.path.join(os.getcwd(), '.env.test')
    if os.path.exists(test_env_path):
        load_dotenv(test_env_path)

    return True

@pytest.fixture
def mock_openai_client():
    """Create a mock OpenAI client for unit tests."""
    mock_client = Mock()

    # Mock response structure
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = "Mock response content"
    mock_response.choices[0].message.tool_calls = None
    mock_response.choices[0].finish_reason = "stop"
    mock_response.usage.prompt_tokens = 10
    mock_response.usage.completion_tokens = 5
    mock_response.usage.total_tokens = 15

    mock_client.chat.completions.create.return_value = mock_response
    return mock_client

@pytest.fixture
def real_openai_client():
    """Create real OpenAI client for integration tests."""
    if not TestConfig.should_use_real_api():
        pytest.skip("Real API tests disabled (set PM_AI_TEST_MODE=integration)")

    api_key = TestConfig.get_openai_key()
    if not api_key:
        pytest.skip("No OpenAI API key available for integration tests")

    from openai import OpenAI
    return OpenAI(api_key=api_key)

@pytest.fixture
def api_call_tracker():
    """Track API calls to enforce cost limits."""
    class APICallTracker:
        def __init__(self):
            self.call_count = 0
            self.max_calls = TestConfig.get_max_api_calls()

        def increment(self):
            self.call_count += 1
            if self.call_count > self.max_calls:
                pytest.fail(f"Exceeded maximum API calls ({self.max_calls}) for test run")

        def get_count(self):
            return self.call_count

        def reset(self):
            self.call_count = 0

    return APICallTracker()

@pytest.fixture
def mock_llm_engine(mock_openai_client):
    """Create LLM engine with mocked client for unit tests."""
    from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig
    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config, mock_client=mock_openai_client)

@pytest.fixture
def real_llm_engine():
    """Create LLM engine with real API for integration tests."""
    if not TestConfig.should_use_real_api():
        pytest.skip("Real API tests disabled (set PM_AI_TEST_MODE=integration)")

    api_key = TestConfig.get_openai_key()
    if not api_key:
        pytest.skip("No OpenAI API key available for integration tests")

    from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig
    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config)

@pytest.fixture
def mock_intent_parser(mock_llm_engine):
    """Create intent parser with mocked LLM engine."""
    from pm_ai.intelligence.intent_parser import IntentParser
    return IntentParser(mock_llm_engine)

@pytest.fixture
def real_intent_parser(real_llm_engine):
    """Create intent parser with real LLM engine for integration tests."""
    from pm_ai.intelligence.intent_parser import IntentParser
    return IntentParser(real_llm_engine)

@pytest.fixture
def mock_memory_store():
    """Create memory store for unit tests."""
    from pm_ai.conversation.memory_store import MemoryStore
    return MemoryStore(test_mode=True)

@pytest.fixture
def mock_session_manager(mock_memory_store):
    """Create session manager with mocked memory store for unit tests."""
    from pm_ai.conversation.session_manager import SessionManager
    return SessionManager(mock_memory_store, test_mode=True)

@pytest.fixture
def mock_context_manager(mock_session_manager):
    """Create context manager with mocked session manager for unit tests."""
    from pm_ai.intelligence.context_manager import ContextManager
    return ContextManager(mock_session_manager, test_mode=True)

@pytest.fixture
def mock_tool_orchestrator(mock_llm_engine):
    """Create tool orchestrator with mocked LLM engine for unit tests."""
    from pm_ai.integration.tool_orchestrator import ToolOrchestrator
    return ToolOrchestrator(mock_llm_engine, test_mode=True)

@pytest.fixture
def mock_result_synthesizer(mock_llm_engine):
    """Create result synthesizer with mocked LLM engine for unit tests."""
    from pm_ai.integration.result_synthesizer import ResultSynthesizer
    return ResultSynthesizer(mock_llm_engine, test_mode=True)

@pytest.fixture
def real_tool_orchestrator(real_llm_engine):
    """Create tool orchestrator with real LLM engine for integration tests."""
    from pm_ai.integration.tool_orchestrator import ToolOrchestrator
    return ToolOrchestrator(real_llm_engine, test_mode=True)

@pytest.fixture
def real_result_synthesizer(real_llm_engine):
    """Create result synthesizer with real LLM engine for integration tests."""
    from pm_ai.integration.result_synthesizer import ResultSynthesizer
    return ResultSynthesizer(real_llm_engine, test_mode=True)

@pytest.fixture
def mock_agent():
    """Create agent with mocked tools for unit tests."""
    from unittest.mock import Mock
    from pm_ai.agents import Agent

    mock_tool = Mock(__name__="get_property_metrics")
    tools = [mock_tool]
    return Agent("test_agent", "Test instructions", tools, test_mode=True)

@pytest.fixture
def real_agent():
    """Create agent with real LLM for integration tests."""
    if not TestConfig.should_use_real_api():
        pytest.skip("Real API tests disabled (set PM_AI_TEST_MODE=integration)")

    api_key = TestConfig.get_openai_key()
    if not api_key:
        pytest.skip("No OpenAI API key available for integration tests")

    from unittest.mock import Mock
    from pm_ai.agents import Agent

    # Mock tools for testing
    mock_tool = Mock(__name__="get_property_metrics")
    tools = [mock_tool]
    return Agent("test_agent", "Test instructions", tools, test_mode=True)

@pytest.fixture
def mock_intent_response():
    """Mock response for intent parsing function calls."""
    return {
        "intent": "property_performance",
        "confidence": 0.95,
        "parameters": {
            "property_ids": ["P123"],
            "time_period": "last_30_days",
            "metrics": ["occupancy_rate", "revenue"]
        },
        "clarification_needed": None,
        "suggested_actions": ["get_property_metrics"]
    }

@pytest.fixture(autouse=True)
def setup_test_environment(load_test_env):
    """Automatically set up test environment for all tests."""
    # Use the load_test_env fixture to ensure environment is loaded
    _ = load_test_env

    # Ensure test mode is set
    if not os.getenv("PM_AI_TEST_MODE"):
        os.environ["PM_AI_TEST_MODE"] = "unit"

    # Set debug mode for tests
    if not os.getenv("PM_AI_DEBUG"):
        os.environ["PM_AI_DEBUG"] = "true"

    yield

    # Cleanup after test if needed
    pass

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests with mocked dependencies"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests with real API calls"
    )
    config.addinivalue_line(
        "markers", "e2e: End-to-end tests with full system"
    )
    config.addinivalue_line(
        "markers", "slow: Tests that take longer than 30 seconds"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle test mode filtering."""
    # Use config parameter to avoid unused parameter warning
    _ = config
    test_mode = TestConfig.get_test_mode()

    # Skip integration tests if in unit test mode
    if test_mode == "unit":
        skip_integration = pytest.mark.skip(reason="Integration tests disabled in unit test mode")
        skip_e2e = pytest.mark.skip(reason="E2E tests disabled in unit test mode")

        for item in items:
            if "integration" in item.keywords:
                item.add_marker(skip_integration)
            elif "e2e" in item.keywords:
                item.add_marker(skip_e2e)
