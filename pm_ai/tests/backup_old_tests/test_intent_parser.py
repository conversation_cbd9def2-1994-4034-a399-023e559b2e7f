"""
Tests for the Intent Parser component.
"""
import unittest
from unittest.mock import patch, MagicMock
import json

from pm_ai.intelligence.intent_parser import IntentParser, QueryIntent, ParsedIntent
from pm_ai.intelligence.llm_engine import LLMEngine

class TestIntentParser(unittest.TestCase):
    """Test cases for IntentParser."""

    def setUp(self):
        """Set up test environment."""
        # Create mock LLM engine
        self.mock_llm_engine = MagicMock(spec=LLMEngine)
        self.parser = IntentParser(self.mock_llm_engine)
    
    def test_initialization(self):
        """Test IntentParser initialization."""
        # Test system prompt creation
        self.assertIsNotNone(self.parser.system_prompt)
        self.assertIn("property management", self.parser.system_prompt.lower())
    
    def test_parse_query_property_performance(self):
        """Test parsing a property performance query."""
        # Mock LLM response for property performance query
        mock_response = {
            "function_call": {
                "name": "extract_intent",
                "arguments": json.dumps({
                    "intent": "property_performance",
                    "confidence": 0.95,
                    "parameters": {
                        "property_ids": ["P123"],
                        "time_period": "last_30_days",
                        "metrics": ["occupancy_rate", "revenue"]
                    }
                })
            },
            "content": None
        }
        
        self.mock_llm_engine.create_completion.return_value = mock_response
        
        # Parse query
        result = self.parser.parse_query("How is property P123 performing in the last 30 days?")
        
        # Verify result
        self.assertEqual(result.intent, QueryIntent.PROPERTY_PERFORMANCE)
        self.assertEqual(result.confidence, 0.95)
        self.assertEqual(result.parameters["property_ids"], ["P123"])
        self.assertEqual(result.parameters["time_period"], "last_30_days")
        self.assertEqual(result.parameters["metrics"], ["occupancy_rate", "revenue"])
    
    def test_parse_query_comparison(self):
        """Test parsing a property comparison query."""
        # Mock LLM response for comparison query
        mock_response = {
            "function_call": {
                "name": "extract_intent",
                "arguments": json.dumps({
                    "intent": "property_comparison",
                    "confidence": 0.92,
                    "parameters": {
                        "property_ids": ["P456"],
                        "comparison_type": "vs cohort",
                        "cohort": "beachfront"
                    }
                })
            },
            "content": None
        }
        
        self.mock_llm_engine.create_completion.return_value = mock_response
        
        # Parse query
        result = self.parser.parse_query("Compare P456 to other beachfront properties.")
        
        # Verify result
        self.assertEqual(result.intent, QueryIntent.PROPERTY_COMPARISON)
        self.assertEqual(result.confidence, 0.92)
        self.assertEqual(result.parameters["property_ids"], ["P456"])
        self.assertEqual(result.parameters["comparison_type"], "vs cohort")
        self.assertEqual(result.parameters["cohort"], "beachfront")
    
    def test_parse_query_with_clarification(self):
        """Test parsing a query that needs clarification."""
        # Mock LLM response for unclear query
        mock_response = {
            "function_call": {
                "name": "extract_intent",
                "arguments": json.dumps({
                    "intent": "clarification_needed",
                    "confidence": 0.5,
                    "parameters": {},
                    "clarification_needed": "Which property would you like me to analyze?",
                    "suggested_actions": ["Ask for property ID"]
                })
            },
            "content": None
        }
        
        self.mock_llm_engine.create_completion.return_value = mock_response
        
        # Parse query
        result = self.parser.parse_query("How is it performing?")
        
        # Verify result
        self.assertEqual(result.intent, QueryIntent.CLARIFICATION_NEEDED)
        self.assertEqual(result.confidence, 0.5)
        self.assertEqual(result.clarification_needed, "Which property would you like me to analyze?")
        self.assertEqual(result.suggested_actions, ["Ask for property ID"])
    
    def test_parse_query_fallback(self):
        """Test fallback when function calling fails."""
        # Mock LLM response for function calling failure
        mock_response = {
            "function_call": None,
            "content": "I'm not sure I understand."
        }
        
        self.mock_llm_engine.create_completion.return_value = mock_response
        
        # Parse query
        result = self.parser.parse_query("This is a completely unrelated query.")
        
        # Verify fallback result
        self.assertEqual(result.intent, QueryIntent.GENERAL_INQUIRY)
        self.assertEqual(result.confidence, 0.5)
        self.assertIsNotNone(result.clarification_needed)

if __name__ == "__main__":
    unittest.main()
