"""
Tests for the conversation memory store and session management components.
"""
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from pm_ai.conversation.memory_store import MemoryStore, ConversationTurn, ConversationSession
from pm_ai.conversation.session_manager import SessionManager

class TestMemoryStore(unittest.TestCase):
    """Test cases for the in-memory conversation storage."""

    def setUp(self):
        """Set up test environment."""
        self.memory_store = MemoryStore()
        
    def test_session_creation(self):
        """Test creating a new session."""
        # Create new session
        session_id = self.memory_store.create_session(user_id="user123")
        
        # Verify session was created
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.memory_store.sessions)
        
        # Verify session properties
        session = self.memory_store.get_session(session_id)
        self.assertEqual(session.user_id, "user123")
        self.assertEqual(len(session.turns), 0)
        self.assertIsInstance(session.start_time, datetime)
        self.assertIsInstance(session.last_activity, datetime)
        
    def test_add_turn(self):
        """Test adding conversation turns to a session."""
        # Create new session
        session_id = self.memory_store.create_session()
        
        # Add turn to session
        turn_id = self.memory_store.add_turn(
            session_id=session_id,
            user_query="How is property P123 performing?",
            parsed_intent={"intent": "property_performance"},
            system_response="Property P123 is performing well.",
            tools_used=["get_property_metrics"],
            metadata={"execution_time": 0.5}
        )
        
        # Verify turn was added
        session = self.memory_store.get_session(session_id)
        self.assertEqual(len(session.turns), 1)
        
        # Verify turn properties
        turn = session.turns[0]
        self.assertEqual(turn.turn_id, turn_id)
        self.assertEqual(turn.user_query, "How is property P123 performing?")
        self.assertEqual(turn.parsed_intent["intent"], "property_performance")
        self.assertEqual(turn.system_response, "Property P123 is performing well.")
        self.assertEqual(turn.tools_used, ["get_property_metrics"])
        self.assertEqual(turn.metadata["execution_time"], 0.5)
        
    def test_get_recent_turns(self):
        """Test retrieving recent conversation turns."""
        # Create new session
        session_id = self.memory_store.create_session()
        
        # Add multiple turns
        for i in range(10):
            self.memory_store.add_turn(
                session_id=session_id,
                user_query=f"Query {i}",
                parsed_intent={"intent": "test"},
                system_response=f"Response {i}"
            )
        
        # Get recent turns (default limit is 5)
        recent_turns = self.memory_store.get_recent_turns(session_id)
        
        # Verify we got the 5 most recent turns
        self.assertEqual(len(recent_turns), 5)
        self.assertEqual(recent_turns[0].user_query, "Query 5")
        self.assertEqual(recent_turns[4].user_query, "Query 9")
        
        # Get specific number of turns
        recent_turns = self.memory_store.get_recent_turns(session_id, count=3)
        
        # Verify count limitation works
        self.assertEqual(len(recent_turns), 3)
        self.assertEqual(recent_turns[0].user_query, "Query 7")
        self.assertEqual(recent_turns[2].user_query, "Query 9")
        
    def test_update_context(self):
        """Test updating session context."""
        # Create new session
        session_id = self.memory_store.create_session()
        
        # Update context
        self.memory_store.update_context(
            session_id=session_id,
            context_updates={"preferred_properties": ["P123", "P456"]}
        )
        
        # Verify context was updated
        session = self.memory_store.get_session(session_id)
        self.assertIn("preferred_properties", session.context)
        self.assertEqual(session.context["preferred_properties"], ["P123", "P456"])
        
        # Update context again (should merge)
        self.memory_store.update_context(
            session_id=session_id,
            context_updates={"favorite_cohort": "beachfront"}
        )
        
        # Verify both context items exist
        session = self.memory_store.get_session(session_id)
        self.assertIn("preferred_properties", session.context)
        self.assertIn("favorite_cohort", session.context)
        self.assertEqual(session.context["favorite_cohort"], "beachfront")
        
    def test_cleanup_old_sessions(self):
        """Test automatic cleanup of old sessions."""
        # Set a very low max_sessions for testing
        self.memory_store.max_sessions = 3
        
        # Create multiple sessions
        session_ids = []
        for i in range(5):  # Create 5 sessions (exceeding max of 3)
            session_id = self.memory_store.create_session()
            session_ids.append(session_id)
        
        # Verify cleanup occurred
        self.assertEqual(len(self.memory_store.sessions), 3)
        
        # Verify oldest sessions were removed
        for session_id in session_ids[:2]:
            self.assertNotIn(session_id, self.memory_store.sessions)
        
        # Verify newest sessions were kept
        for session_id in session_ids[2:]:
            self.assertIn(session_id, self.memory_store.sessions)


class TestSessionManager(unittest.TestCase):
    """Test cases for the session manager."""
    
    def setUp(self):
        """Set up test environment."""
        self.memory_store = MemoryStore()
        self.session_manager = SessionManager(self.memory_store)
        
    def test_start_session(self):
        """Test starting a new session."""
        # Start new session
        session_id = self.session_manager.start_session(user_id="user456")
        
        # Verify session was created
        self.assertIsNotNone(session_id)
        self.assertEqual(self.session_manager.active_session_id, session_id)
        
        # Verify session exists in memory store
        session = self.memory_store.get_session(session_id)
        self.assertIsNotNone(session)
        self.assertEqual(session.user_id, "user456")
        
    def test_get_or_create_session(self):
        """Test getting existing session or creating new one."""
        # Start with no active session
        self.assertIsNone(self.session_manager.active_session_id)
        
        # Get or create should create a new session
        session_id = self.session_manager.get_or_create_session()
        self.assertIsNotNone(session_id)
        self.assertEqual(self.session_manager.active_session_id, session_id)
        
        # Get or create with the same session ID should return that session
        same_session_id = self.session_manager.get_or_create_session(session_id)
        self.assertEqual(same_session_id, session_id)
        
        # Get or create with an invalid session ID should create new session
        invalid_id = "invalid-session-id"
        new_session_id = self.session_manager.get_or_create_session(invalid_id)
        self.assertNotEqual(new_session_id, invalid_id)
        
    def test_add_interaction(self):
        """Test adding a complete interaction to the session."""
        # Start new session
        session_id = self.session_manager.start_session()
        
        # Add interaction
        turn_id = self.session_manager.add_interaction(
            user_query="How is property P123 performing?",
            parsed_intent={"intent": "property_performance"},
            system_response="Property P123 is performing well.",
            tools_used=["get_property_metrics"]
        )
        
        # Verify turn was added
        self.assertIsNotNone(turn_id)
        
        # Verify the turn exists in memory store
        session = self.memory_store.get_session(session_id)
        self.assertEqual(len(session.turns), 1)
        self.assertEqual(session.turns[0].turn_id, turn_id)
        
    def test_build_conversation_context(self):
        """Test building conversation context for LLM prompt."""
        # Start new session
        session_id = self.session_manager.start_session()
        
        # Add multiple interactions
        self.session_manager.add_interaction(
            user_query="How is property P123 performing?",
            parsed_intent={"intent": "property_performance", "parameters": {"property_ids": ["P123"]}},
            system_response="Property P123 has 85% occupancy."
        )
        
        self.session_manager.add_interaction(
            user_query="What about P456?",
            parsed_intent={"intent": "property_performance", "parameters": {"property_ids": ["P456"]}},
            system_response="Property P456 has 92% occupancy."
        )
        
        # Build conversation context
        context = self.session_manager.build_conversation_context()
        
        # Verify context structure
        self.assertIn("history", context)
        self.assertIn("references", context)
        self.assertIn("preferences", context)
        
        # Verify history
        self.assertEqual(len(context["history"]), 2)
        self.assertEqual(context["history"][0]["user"], "How is property P123 performing?")
        self.assertEqual(context["history"][1]["user"], "What about P456?")
        
        # Verify references
        self.assertIn("properties", context["references"])
        self.assertIn("P123", context["references"]["properties"])
        self.assertIn("P456", context["references"]["properties"])


if __name__ == "__main__":
    unittest.main()
