"""
Tests for the LLM-powered agent runner that orchestrates the end-to-end flow.
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
from dotenv import load_dotenv
from openai import OpenAI
# Import get_vcr_instance from conftest
from conftest import get_vcr_instance

# Ensure we're using the latest environment variables
load_dotenv()

from pm_ai.agents.llm_agent_runner import LLMAgentRunner
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

# Get the API key from environment
api_key = os.environ.get("OPENAI_API_KEY")
if not api_key or len(api_key) < 10:
    raise ValueError(f"Invalid API key for tests: {api_key[:4] if api_key else None}")
    
print(f"Agent runner test using API key: {api_key[:4]}...")

# Get the standardized VCR instance for this test file using the shared configuration
my_vcr = get_vcr_instance('llm_agent_runner')

class TestLLMAgentRunner(unittest.TestCase):
    """Test cases for the LLMAgentRunner."""

    def setUp(self):
        """Set up test environment."""
        # Use the API key we already verified above
        
        # Create mock tools
        self.mock_tools = {
            "get_property_metrics": MagicMock(return_value={
                "occupancy_rate": 0.85,
                "average_daily_rate": 150.25,
                "revenue": 12500
            }),
            "get_property_comparisons": MagicMock(return_value={
                "property_id": "P123",
                "cohort_avg_occupancy": 0.78,
                "difference": 0.07
            }),
            "get_property_recommendations": MagicMock(return_value=[
                "Increase pricing during peak season",
                "Improve property photos"
            ]),
        }
    
    @my_vcr.use_cassette('test_process_query_end_to_end.yaml')
    @patch("pm_ai.intelligence.llm_engine.LLMEngine.create_completion")
    def test_process_query_end_to_end(self, mock_create_completion):
        """Test the complete query processing flow."""
        # Set up mocks for the different LLM calls
        
        # First LLM call: Intent parsing
        intent_response = {
            "function_call": {
                "name": "extract_intent",
                "arguments": {
                    "intent": "property_performance",
                    "confidence": 0.95,
                    "parameters": {
                        "property_ids": ["P123"],
                        "time_period": "last_30_days"
                    }
                }
            }
        }
        
        # Second LLM call: Response generation
        response_text = "Property P123 has an occupancy rate of 85%, which is excellent. The average daily rate is $150.25 and it has generated $12,500 in revenue over the last 30 days."
        synthesis_response = {
            "content": response_text
        }
        
        # Set up the mock to return different values on successive calls
        mock_create_completion.side_effect = [intent_response, synthesis_response]
        
        # Create the agent runner
        runner = LLMAgentRunner(self.mock_tools)
        
        # Process a query
        query = "How is property P123 performing in the last 30 days?"
        result = runner.process_query(query)
        
        # Verify LLM was called twice (once for intent, once for response)
        self.assertEqual(mock_create_completion.call_count, 2)
        
        # Verify tool was called with correct parameters
        self.mock_tools["get_property_metrics"].assert_called_once()
        
        # Verify response
        self.assertEqual(result["response"], response_text)
        self.assertIn("tools_used", result)
        self.assertEqual(result["tools_used"], ["get_property_metrics"])
        
        # Verify session ID is returned
        self.assertIsNotNone(result["session_id"])
    
    @my_vcr.use_cassette('test_clarification_flow.yaml')
    @patch("pm_ai.intelligence.llm_engine.LLMEngine.create_completion")
    def test_clarification_flow(self, mock_create_completion):
        """Test the flow when clarification is needed."""
        # Set up mock for intent parsing with clarification needed
        clarification_message = "Which property would you like me to analyze?"
        intent_response = {
            "function_call": {
                "name": "extract_intent",
                "arguments": {
                    "intent": "clarification_needed",
                    "confidence": 0.5,
                    "parameters": {},
                    "clarification_needed": clarification_message
                }
            }
        }
        
        mock_create_completion.return_value = intent_response
        
        # Create the agent runner
        runner = LLMAgentRunner(self.mock_tools)
        
        # Process a query requiring clarification
        query = "How is it performing?"
        result = runner.process_query(query)
        
        # Verify LLM was called only once (for intent parsing)
        mock_create_completion.assert_called_once()
        
        # Verify no tools were called
        for tool_mock in self.mock_tools.values():
            tool_mock.assert_not_called()
        
        # Verify clarification response
        self.assertEqual(result["response"], clarification_message)
        self.assertEqual(result["tools_used"], [])
    
    @my_vcr.use_cassette('test_register_tool.yaml')
    @patch("pm_ai.intelligence.llm_engine.LLMEngine")
    def test_register_tool(self, mock_llm_engine):
        """Test registering additional tools."""
        # Create the agent runner
        runner = LLMAgentRunner()
        
        # Mock a new tool function
        new_tool = MagicMock()
        
        # Register the tool
        runner.register_tool("new_test_tool", new_tool)
        
        # Verify tool was registered
        self.assertIn("new_test_tool", runner.available_tools)
        self.assertEqual(runner.available_tools["new_test_tool"], new_tool)

if __name__ == "__main__":
    unittest.main()
