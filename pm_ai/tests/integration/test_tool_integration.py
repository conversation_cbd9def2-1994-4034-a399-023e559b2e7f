"""
Integration tests for tool orchestration with real tool functions.
"""
import pytest
from unittest.mock import Mock

from pm_ai.integration.tool_orchestrator import ToolOrchestrator, ToolResult
from pm_ai.integration.result_synthesizer import ResultSynthesizer
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.integration
class TestToolIntegration:
    """Integration tests for tool orchestration with real tool functions."""

    def test_real_tool_orchestration_flow(self, real_llm_engine, api_call_tracker):
        """Test complete tool orchestration flow with real LLM."""
        api_call_tracker.increment()

        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=True)
        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Mock property management tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 0.85,
                "revenue": 12500,
                "time_period": time_period,
                "metrics_requested": metrics or ["occupancy_rate", "revenue"]
            }

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        # Create parsed intent
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "time_period": "last_30_days",
                "metrics": ["occupancy_rate", "revenue"]
            }
        )

        # Execute tools
        tool_results = orchestrator.execute_tools(parsed_intent, available_tools)

        # Verify tool execution
        assert len(tool_results) == 1
        assert tool_results[0].success is True
        assert tool_results[0].data["occupancy_rate"] == 0.85

        # Synthesize response
        response = synthesizer.synthesize_response(
            "How is property P123 performing?",
            parsed_intent.__dict__,
            tool_results
        )

        # Verify response quality
        assert isinstance(response, str)
        assert len(response) > 50  # Should be a substantial response
        assert orchestrator.get_successful_executions() == 1
        assert synthesizer.get_synthesis_count() == 1

    def test_real_comparison_tool_flow(self, real_llm_engine, api_call_tracker):
        """Test comparison tool flow with real LLM."""
        api_call_tracker.increment()

        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=True)
        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Mock comparison tools
        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {
                "property_id": property_id,
                "comparison_type": comparison_type,
                "performance_vs_cohort": {
                    "occupancy_rate": {"property": 0.85, "cohort_avg": 0.78, "difference": "+7%"},
                    "revenue": {"property": 12500, "cohort_avg": 11200, "difference": "+$1,300"}
                }
            }

        available_tools = {"get_property_comparisons": mock_get_property_comparisons}

        # Create comparison intent
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={
                "property_ids": ["P456"],
                "comparison_type": "vs_cohort",
                "time_period": "last_30_days"
            }
        )

        # Execute and synthesize
        tool_results = orchestrator.execute_tools(parsed_intent, available_tools)
        response = synthesizer.synthesize_response(
            "How does P456 compare to similar properties?",
            parsed_intent.__dict__,
            tool_results
        )

        # Verify results
        assert tool_results[0].success is True
        assert "vs_cohort" in str(tool_results[0].data)
        assert isinstance(response, str)
        assert len(response) > 50

    def test_real_error_handling_flow(self, real_llm_engine, api_call_tracker):
        """Test error handling with real LLM synthesis."""
        api_call_tracker.increment()

        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=True)
        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Mock failing tool
        def mock_failing_tool(property_id):
            raise ValueError(f"Property {property_id} not found in database")

        available_tools = {"get_property_metrics": mock_failing_tool}

        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P999"]}
        )

        # Execute tools (should fail)
        tool_results = orchestrator.execute_tools(parsed_intent, available_tools)

        # Synthesize error response
        response = synthesizer.synthesize_response(
            "How is P999 performing?",
            parsed_intent.__dict__,
            tool_results
        )

        # Verify error handling
        assert tool_results[0].success is False
        assert "not found" in tool_results[0].error_message
        assert orchestrator.get_failed_executions() == 1
        assert synthesizer.get_error_response_count() == 1
        assert isinstance(response, str)
        assert len(response) > 30  # Should provide helpful error message

    def test_real_multi_tool_orchestration(self, real_llm_engine, api_call_tracker):
        """Test orchestration with multiple tools."""
        api_call_tracker.increment()

        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=True)

        # Mock multiple tools for comparison intent
        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {"property_id": property_id, "comparison_data": "mock_data"}

        def mock_get_cohort_properties(cohort="similar", property_id=None):
            return {"cohort": cohort, "properties": ["P100", "P101", "P102"]}

        available_tools = {
            "get_property_comparisons": mock_get_property_comparisons,
            "get_cohort_properties": mock_get_cohort_properties
        }

        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "comparison_type": "vs_cohort"}
        )

        # Execute multiple tools
        tool_results = orchestrator.execute_tools(parsed_intent, available_tools)

        # Should execute both tools for comparison intent
        assert len(tool_results) == 2
        assert all(result.success for result in tool_results)
        assert orchestrator.get_successful_executions() == 2

    @pytest.mark.slow
    def test_real_performance_with_large_data(self, real_llm_engine, api_call_tracker):
        """Test performance with larger data sets (marked as slow)."""
        api_call_tracker.increment()

        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=True)
        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Mock tool returning large dataset
        def mock_large_data_tool(property_id):
            return {
                "property_id": property_id,
                "detailed_metrics": {f"metric_{i}": i * 100 for i in range(50)},
                "historical_data": [{"month": f"2024-{i:02d}", "value": i * 1000} for i in range(1, 13)]
            }

        available_tools = {"get_property_metrics": mock_large_data_tool}

        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )

        # Execute and synthesize
        tool_results = orchestrator.execute_tools(parsed_intent, available_tools)
        response = synthesizer.synthesize_response(
            "Give me detailed performance data for P123",
            parsed_intent.__dict__,
            tool_results
        )

        # Should handle large data efficiently
        assert tool_results[0].success is True
        assert len(str(tool_results[0].data)) > 1000  # Large data
        assert isinstance(response, str)
        assert tool_results[0].execution_time < 5.0  # Should be reasonably fast

    def test_real_fallback_response_quality(self, real_llm_engine, api_call_tracker):
        """Test quality of fallback responses with real LLM."""
        api_call_tracker.increment()

        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Test fallback with clarification needed
        response = synthesizer.synthesize_response(
            "Tell me about stuff",
            {"intent": "general_inquiry", "clarification_needed": "Could you be more specific about which property you're asking about?"},
            []
        )

        assert "specific" in response.lower()
        assert synthesizer.get_fallback_count() == 1

    def test_real_context_aware_synthesis(self, real_llm_engine, api_call_tracker):
        """Test context-aware response synthesis."""
        api_call_tracker.increment()

        synthesizer = ResultSynthesizer(real_llm_engine, test_mode=True)

        # Mock tool result
        tool_results = [
            ToolResult(
                tool_name="get_property_metrics",
                success=True,
                data={"occupancy_rate": 0.92, "revenue": 15000},
                execution_time=0.3
            )
        ]

        # Include conversation context
        conversation_context = {
            "history": [
                {"user": "How is P123 doing?", "system": "P123 is performing well."}
            ],
            "references": {"properties": {"P123": "2024-01-01T10:00:00"}}
        }

        response = synthesizer.synthesize_response(
            "What about compared to last month?",
            {"intent": "property_performance"},
            tool_results,
            conversation_context
        )

        # Should incorporate context
        assert isinstance(response, str)
        assert len(response) > 50
        assert synthesizer.get_synthesis_count() == 1
