"""
Integration tests for intent parser using real OpenAI API.
"""
import pytest

from pm_ai.intelligence.intent_parser import IntentParser, QueryIntent

@pytest.mark.integration
class TestIntentParserIntegration:
    """Integration tests using real OpenAI API with cost controls."""

    def test_real_property_performance_parsing(self, real_llm_engine, api_call_tracker):
        """Test property performance intent with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        result = parser.parse_query("How is property P123 performing this month?")

        assert result.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert result.confidence > 0.7
        assert "property_ids" in result.parameters or "P123" in str(result.parameters)
        assert parser.get_parse_count() == 1

    def test_real_natural_language_variations(self, real_llm_engine, api_call_tracker):
        """Test natural language variations with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        # Test natural language query
        result = parser.parse_query("How's my beachfront place doing lately?")

        assert result.intent in [QueryIntent.PROPERTY_PERFORMANCE, QueryIntent.COHORT_ANALYSIS]
        assert result.confidence > 0.5
        assert "beachfront" in str(result.parameters).lower() or "cohort" in result.parameters

    def test_real_comparison_intent(self, real_llm_engine, api_call_tracker):
        """Test comparison intent parsing with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        result = parser.parse_query("Compare P456 to similar properties")

        assert result.intent == QueryIntent.PROPERTY_COMPARISON
        assert result.confidence > 0.7
        assert "P456" in str(result.parameters) or "property_ids" in result.parameters

    def test_real_ambiguous_query_handling(self, real_llm_engine, api_call_tracker):
        """Test handling of ambiguous queries with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        result = parser.parse_query("Tell me about stuff")

        # Should either ask for clarification or have low confidence
        assert (result.intent == QueryIntent.CLARIFICATION_NEEDED or
                result.confidence < 0.7 or
                result.clarification_needed is not None)

    @pytest.mark.slow
    def test_real_context_awareness(self, real_llm_engine, api_call_tracker):
        """Test context-aware parsing with real API (marked as slow)."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        context = {
            "previous_property": "P123",
            "last_query_type": "property_performance"
        }

        result = parser.parse_query("What about compared to last year?", context)

        # Should understand the context reference
        assert result.intent in [QueryIntent.PROPERTY_PERFORMANCE, QueryIntent.PROPERTY_COMPARISON]
        assert result.confidence > 0.5

    def test_real_recommendations_intent(self, real_llm_engine, api_call_tracker):
        """Test recommendations intent with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        result = parser.parse_query("What recommendations do you have for improving P789?")

        assert result.intent == QueryIntent.RECOMMENDATIONS
        assert result.confidence > 0.7
        assert "P789" in str(result.parameters) or "property_ids" in result.parameters

    def test_real_cohort_analysis_intent(self, real_llm_engine, api_call_tracker):
        """Test cohort analysis intent with real API."""
        api_call_tracker.increment()

        parser = IntentParser(real_llm_engine)
        result = parser.parse_query("How are all my downtown properties performing?")

        assert result.intent in [QueryIntent.COHORT_ANALYSIS, QueryIntent.PROPERTY_PERFORMANCE]
        assert result.confidence > 0.6
        assert "downtown" in str(result.parameters).lower() or "cohort" in result.parameters
