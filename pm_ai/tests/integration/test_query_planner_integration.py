"""
Integration tests for query planner with real LLM calls.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.query_planner import QueryPlanner, TaskType
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.integration
class TestQueryPlannerIntegration:
    """Integration tests for query planner with real LLM calls."""

    def test_real_property_performance_planning(self, real_llm_engine, api_call_tracker):
        """Test query planning for property performance with real LLM."""
        # Note: This test doesn't make LLM calls yet, but sets up the infrastructure
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock property tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 85,
                "average_daily_rate": 150.0,
                "total_revenue": 12500.0,
                "bookings": 25,
                "time_period": time_period
            }

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        # Create and execute plan
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "original_query": "How is property P123 performing this month?"
            }
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Verify plan quality
        assert result_plan.completed is True
        assert result_plan.final_result is not None
        assert result_plan.final_result["tasks_completed"] > 0
        
        # Check that data retrieval task was executed
        data_tasks = [task for task in result_plan.tasks if task.task_type == TaskType.DATA_RETRIEVAL]
        assert len(data_tasks) > 0
        assert all(task.completed for task in data_tasks)

    def test_real_comparison_planning_execution(self, real_llm_engine, api_call_tracker):
        """Test query planning and execution for property comparison."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock comparison tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 88, "revenue": 13000}

        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {
                "property_id": property_id,
                "comparison_data": {
                    "occupancy_rate": {
                        "property_value": 88,
                        "cohort_avg": 78,
                        "percentile": 85
                    }
                }
            }

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_comparisons": mock_get_property_comparisons
        }

        # Create comparison plan
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={
                "property_ids": ["P456"],
                "comparison_type": "vs_cohort",
                "original_query": "How does P456 compare to similar properties?"
            }
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Verify comparison planning
        assert result_plan.completed is True
        assert result_plan.complexity_score > 0
        
        # Should have comparison tasks
        comparison_tasks = [task for task in result_plan.tasks if task.task_type == TaskType.COMPARISON]
        assert len(comparison_tasks) > 0

    def test_real_recommendations_planning(self, real_llm_engine, api_call_tracker):
        """Test query planning for recommendations."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock recommendation tools
        def mock_get_property_recommendations(property_id):
            return {
                "property_id": property_id,
                "pricing_recommendations": [
                    {
                        "recommendation": "Increase weekend rates by 10%",
                        "reasoning": "High demand on weekends",
                        "potential_impact": "+$500 monthly revenue"
                    }
                ],
                "operational_recommendations": [
                    {
                        "recommendation": "Improve listing photos",
                        "reasoning": "Current photos underperform",
                        "potential_impact": "+15% booking conversion"
                    }
                ]
            }

        available_tools = {"get_property_recommendations": mock_get_property_recommendations}

        # Create recommendations plan
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={
                "property_ids": ["P789"],
                "original_query": "What recommendations do you have for improving P789?"
            }
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Verify recommendations planning
        assert result_plan.completed is True
        assert len(result_plan.tasks) >= 4  # Should have multiple reasoning steps
        
        # Should include validation for recommendations
        validation_tasks = [task for task in result_plan.tasks if task.task_type == TaskType.VALIDATION]
        assert len(validation_tasks) > 0

    def test_real_multi_property_planning(self, real_llm_engine, api_call_tracker):
        """Test planning for multiple properties."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock tools for multiple properties
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            property_data = {
                "P123": {"occupancy_rate": 85, "revenue": 12000},
                "P456": {"occupancy_rate": 78, "revenue": 10500},
                "P789": {"occupancy_rate": 92, "revenue": 15000}
            }
            return {"property_id": property_id, **property_data.get(property_id, {})}

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        # Create multi-property plan
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123", "P456", "P789"],
                "original_query": "How are my properties P123, P456, and P789 performing?"
            }
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Verify multi-property handling
        assert result_plan.completed is True
        
        # Should have parallel data retrieval tasks
        data_tasks = [task for task in result_plan.tasks if task.task_type == TaskType.DATA_RETRIEVAL]
        assert len(data_tasks) >= 3  # At least one per property

    def test_real_complex_query_planning(self, real_llm_engine, api_call_tracker):
        """Test planning for complex queries with multiple requirements."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock comprehensive tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "comprehensive_metrics": "detailed_data"}

        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {"property_id": property_id, "comparison_analysis": "detailed_comparison"}

        def mock_get_property_recommendations(property_id):
            return {"property_id": property_id, "recommendations": "actionable_insights"}

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_comparisons": mock_get_property_comparisons,
            "get_property_recommendations": mock_get_property_recommendations
        }

        # Complex query requiring multiple analysis types
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "analysis_types": ["performance", "comparison", "recommendations"],
                "original_query": "Give me a comprehensive analysis of P123 with performance, comparisons, and recommendations"
            }
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Verify complex planning
        assert result_plan.completed is True
        assert result_plan.complexity_score > 5  # Should be complex
        assert len(result_plan.tasks) >= 5  # Multiple reasoning steps

    def test_real_planning_error_handling(self, real_llm_engine, api_call_tracker):
        """Test error handling in query planning."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock failing tool
        def mock_failing_tool(property_id):
            raise ValueError(f"Property {property_id} not found")

        available_tools = {"get_property_metrics": mock_failing_tool}

        # Create plan
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P999"]}
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Should handle errors gracefully
        failed_tasks = [task for task in result_plan.tasks if task.error_message]
        assert len(failed_tasks) > 0
        
        # Plan should still complete
        assert result_plan.final_result is not None

    def test_real_planning_optimization_quality(self, real_llm_engine, api_call_tracker):
        """Test quality of planning optimization."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock tools with different performance characteristics
        def mock_fast_tool(property_id):
            return {"property_id": property_id, "data": "fast_result"}

        def mock_slow_tool(property_id):
            return {"property_id": property_id, "data": "slow_result"}

        available_tools = {
            "get_property_metrics": mock_fast_tool,
            "get_property_comparisons": mock_slow_tool
        }

        # Create plan that can benefit from optimization
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={"property_ids": ["P123", "P456"]}
        )

        plan = planner.create_query_plan(parsed_intent)
        
        # Verify optimization quality
        assert plan.estimated_total_duration > 0
        assert plan.confidence > 0.5
        
        # Execution order should be optimized
        assert len(plan.execution_order) == len(plan.tasks)
        
        # Critical tasks should come first
        first_task = planner._get_task_by_id(plan, plan.execution_order[0])
        assert first_task.priority.value in ["critical", "high"]

    def test_real_planning_confidence_accuracy(self, real_llm_engine, api_call_tracker):
        """Test accuracy of planning confidence scores."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # High-confidence scenario
        high_confidence_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.95,
            parameters={"property_ids": ["P123"]}
        )
        
        # Low-confidence scenario
        low_confidence_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.6,
            parameters={"property_ids": ["P123", "P456", "P789", "P999"]}  # Complex
        )
        
        high_plan = planner.create_query_plan(high_confidence_intent)
        low_plan = planner.create_query_plan(low_confidence_intent)
        
        # High confidence input should result in higher plan confidence
        assert high_plan.confidence > low_plan.confidence
        assert high_plan.complexity_score < low_plan.complexity_score

    def test_real_planning_scalability(self, real_llm_engine, api_call_tracker):
        """Test planning scalability with larger datasets."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock tool for many properties
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "metrics": "standard_data"}

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        # Large-scale query
        property_ids = [f"P{i:03d}" for i in range(1, 21)]  # 20 properties
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": property_ids}
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Should handle large scale efficiently
        assert result_plan.completed is True
        assert result_plan.estimated_total_duration < 30  # Should be reasonable
        
        # Should have parallel execution for scalability
        data_tasks = [task for task in result_plan.tasks if task.task_type == TaskType.DATA_RETRIEVAL]
        assert len(data_tasks) > 1  # Should have parallel data retrieval

    @pytest.mark.slow
    def test_real_end_to_end_planning_execution(self, real_llm_engine, api_call_tracker):
        """Test complete end-to-end planning and execution (marked as slow)."""
        planner = QueryPlanner(real_llm_engine, test_mode=True)
        
        # Mock comprehensive tool suite
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "performance_data": "comprehensive"}

        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {"property_id": property_id, "comparison_data": "detailed"}

        def mock_get_property_recommendations(property_id):
            return {"property_id": property_id, "recommendations": "actionable"}

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_comparisons": mock_get_property_comparisons,
            "get_property_recommendations": mock_get_property_recommendations
        }

        # End-to-end test
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )

        plan = planner.create_query_plan(parsed_intent)
        result_plan = planner.execute_query_plan(plan, available_tools)

        # Comprehensive verification
        assert result_plan.completed is True
        assert all(task.completed for task in result_plan.tasks if task.tool_name)
        assert result_plan.final_result["tasks_completed"] == result_plan.final_result["total_tasks"]
        assert planner.get_successful_plans() == 1
