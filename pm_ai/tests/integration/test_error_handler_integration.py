"""
Integration tests for context-aware error handler with real LLM calls.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.error_handler import (
    ContextAwareError<PERSON><PERSON><PERSON>, ErrorContext, ErrorType, ErrorSeverity, RecoveryStrategy
)
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.integration
class TestErrorHandlerIntegration:
    """Integration tests for error handler with real LLM calls."""

    def test_real_parsing_error_recovery(self, real_llm_engine, api_call_tracker):
        """Test parsing error recovery with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me stuff about my place",
            error_message="Could not identify property or specific request",
            parsed_intent=ParsedIntent(
                intent=QueryIntent.GENERAL_INQUIRY,
                confidence=0.3,
                parameters={}
            )
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Verify recovery quality
        assert isinstance(recovery_plan.clarification_message, str)
        assert len(recovery_plan.clarification_message) > 50
        assert any(term in recovery_plan.clarification_message.lower() for term in ["property", "specify", "clarify"])
        assert handler.get_errors_handled() == 1

    def test_real_tool_execution_error_recovery(self, real_llm_engine, api_call_tracker):
        """Test tool execution error recovery with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.TOOL_EXECUTION_ERROR,
            severity=ErrorSeverity.HIGH,
            original_query="How is property P999 performing?",
            error_message="Property P999 not found in database",
            parsed_intent=ParsedIntent(
                intent=QueryIntent.PROPERTY_PERFORMANCE,
                confidence=0.9,
                parameters={"property_ids": ["P999"]}
            )
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Verify recovery quality
        assert len(recovery_plan.clarification_message) > 50
        assert any(term in recovery_plan.clarification_message.lower() for term in ["p999", "not found", "alternative"])
        assert recovery_plan.strategy in [RecoveryStrategy.SUGGEST_ALTERNATIVES, RecoveryStrategy.RETRY_WITH_DEFAULTS]

    def test_real_ambiguous_query_recovery(self, real_llm_engine, api_call_tracker):
        """Test ambiguous query recovery with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="Compare them",
            error_message="Ambiguous reference - unclear what to compare",
            conversation_history=[
                {"user": "Show me properties", "system": "Here are your properties: P123, P456, P789"},
                {"user": "Compare them", "system": "Error: ambiguous reference"}
            ]
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Verify contextual recovery
        assert len(recovery_plan.clarification_message) > 50
        assert any(term in recovery_plan.clarification_message.lower() for term in ["compare", "specify", "which"])
        # Should reference the conversation context
        assert len(recovery_plan.suggested_queries) > 0

    def test_real_missing_parameters_recovery(self, real_llm_engine, api_call_tracker):
        """Test missing parameters recovery with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.MISSING_PARAMETERS,
            severity=ErrorSeverity.MEDIUM,
            original_query="Get performance metrics",
            error_message="Missing required parameter: property_id",
            parsed_intent=ParsedIntent(
                intent=QueryIntent.PROPERTY_PERFORMANCE,
                confidence=0.8,
                parameters={"metrics": ["occupancy_rate", "revenue"]}
            )
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Verify parameter request quality
        assert len(recovery_plan.clarification_message) > 50
        assert any(term in recovery_plan.clarification_message.lower() for term in ["property", "specify", "id"])
        assert len(recovery_plan.required_parameters) > 0
        assert "property_id" in recovery_plan.required_parameters

    def test_real_system_error_recovery(self, real_llm_engine, api_call_tracker):
        """Test system error recovery with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.SYSTEM_ERROR,
            severity=ErrorSeverity.CRITICAL,
            original_query="How is P123 performing?",
            error_message="Database connection timeout",
            system_state={"database_status": "unavailable", "last_update": "2 hours ago"}
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Verify system error handling
        assert recovery_plan.strategy in [RecoveryStrategy.FALLBACK_RESPONSE, RecoveryStrategy.ESCALATE_TO_HUMAN]
        assert len(recovery_plan.clarification_message) > 50
        assert any(term in recovery_plan.clarification_message.lower() for term in ["technical", "issue", "try"])

    def test_real_conversation_context_awareness(self, real_llm_engine, api_call_tracker):
        """Test conversation context awareness in error recovery."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        # Rich conversation history
        conversation_history = [
            {"user": "How is P123 performing?", "system": "P123 has 85% occupancy rate..."},
            {"user": "What about P456?", "system": "P456 has 78% occupancy rate..."},
            {"user": "Compare them", "system": "I need clarification on what to compare"},
            {"user": "The revenue", "system": "Error: ambiguous comparison request"}
        ]
        
        error_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="The revenue",
            error_message="Ambiguous comparison - unclear parameters",
            conversation_history=conversation_history
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Should leverage conversation context
        assert len(recovery_plan.clarification_message) > 100  # Should be detailed
        assert any(term in recovery_plan.clarification_message.lower() for term in ["p123", "p456", "revenue"])

    def test_real_escalation_after_multiple_attempts(self, real_llm_engine, api_call_tracker):
        """Test error handling escalation after multiple recovery attempts."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me the thing",
            error_message="Still cannot parse user intent",
            recovery_attempts=3,  # Multiple failed attempts
            conversation_history=[
                {"user": "Show me the thing", "system": "Could you clarify..."},
                {"user": "The property thing", "system": "I need more specific..."},
                {"user": "Just show me the thing", "system": "Error: still unclear"}
            ]
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Should escalate strategy after multiple attempts
        assert recovery_plan.strategy in [RecoveryStrategy.PROVIDE_EXAMPLES, RecoveryStrategy.FALLBACK_RESPONSE]
        assert len(recovery_plan.clarification_message) > 50

    def test_real_error_severity_escalation(self, real_llm_engine, api_call_tracker):
        """Test error severity escalation with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        # Error with escalation indicators
        error_context = ErrorContext(
            error_type=ErrorType.PARSING_ERROR,
            severity=None,  # Will be classified
            original_query="Critical system failure query",
            error_message="Internal system error - critical failure detected",
            recovery_attempts=3
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Should escalate severity and strategy
        classified_severity = handler._classify_error_severity(error_context)
        assert classified_severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]

    def test_real_recovery_plan_quality_assessment(self, real_llm_engine, api_call_tracker):
        """Test quality assessment of recovery plans with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.INSUFFICIENT_CONTEXT,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show performance",
            error_message="Missing property specification",
            user_context={"user_type": "property_manager", "properties_owned": ["P123", "P456", "P789"]}
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Assess recovery plan quality
        assert recovery_plan.confidence > 0.5
        assert len(recovery_plan.clarification_message) > 50
        # Should be helpful and specific
        assert any(term in recovery_plan.clarification_message.lower() for term in ["property", "specify", "which"])

    def test_real_contextual_suggestion_generation(self, real_llm_engine, api_call_tracker):
        """Test contextual suggestion generation with real LLM."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        error_context = ErrorContext(
            error_type=ErrorType.AMBIGUOUS_QUERY,
            severity=ErrorSeverity.MEDIUM,
            original_query="Show me analytics",
            error_message="Ambiguous analytics request",
            user_context={
                "recent_properties": ["P123", "P456"],
                "common_queries": ["performance", "comparison", "recommendations"]
            }
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Should generate contextual suggestions
        assert len(recovery_plan.suggested_queries) > 0
        # Suggestions should be relevant to user context
        suggested_text = " ".join(recovery_plan.suggested_queries).lower()
        assert any(term in suggested_text for term in ["p123", "p456", "performance", "comparison"])

    @pytest.mark.slow
    def test_real_comprehensive_error_handling_flow(self, real_llm_engine, api_call_tracker):
        """Test comprehensive error handling flow (marked as slow)."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        # Complex error scenario
        error_context = ErrorContext(
            error_type=ErrorType.TOOL_EXECUTION_ERROR,
            severity=ErrorSeverity.HIGH,
            original_query="Give me a comprehensive analysis of all my beachfront properties with recommendations",
            error_message="Multiple tool failures: property lookup failed, comparison service unavailable",
            parsed_intent=ParsedIntent(
                intent=QueryIntent.RECOMMENDATIONS,
                confidence=0.8,
                parameters={"cohort": "beachfront", "analysis_type": "comprehensive"}
            ),
            conversation_history=[
                {"user": "How are my properties doing?", "system": "Your properties are performing well overall..."},
                {"user": "What about beachfront specifically?", "system": "Let me check beachfront properties..."}
            ],
            user_context={"property_portfolio": "mixed", "focus_area": "beachfront"},
            recovery_attempts=1
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Comprehensive verification
        assert len(recovery_plan.clarification_message) > 100
        assert recovery_plan.confidence > 0.6
        assert any(term in recovery_plan.clarification_message.lower() for term in ["beachfront", "analysis", "alternative"])

    def test_real_error_pattern_learning(self, real_llm_engine, api_call_tracker):
        """Test error pattern learning and adaptation."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        # Similar error patterns
        similar_errors = [
            ErrorContext(
                error_type=ErrorType.PARSING_ERROR,
                severity=ErrorSeverity.MEDIUM,
                original_query="Show me stuff",
                error_message="Vague request"
            ),
            ErrorContext(
                error_type=ErrorType.PARSING_ERROR,
                severity=ErrorSeverity.MEDIUM,
                original_query="Give me things",
                error_message="Unclear request"
            )
        ]
        
        recovery_plans = []
        for error_context in similar_errors:
            plan = handler.handle_error(error_context)
            recovery_plans.append(plan)
        
        # Should handle similar patterns consistently
        assert len(recovery_plans) == 2
        assert all(plan.strategy == recovery_plans[0].strategy for plan in recovery_plans)
        assert handler.get_errors_handled() == 2

    def test_real_recovery_effectiveness_measurement(self, real_llm_engine, api_call_tracker):
        """Test measurement of recovery effectiveness."""
        api_call_tracker.increment()

        handler = ContextAwareErrorHandler(real_llm_engine, test_mode=True)
        
        # Simulate successful recovery
        error_context = ErrorContext(
            error_type=ErrorType.MISSING_PARAMETERS,
            severity=ErrorSeverity.MEDIUM,
            original_query="Get metrics",
            error_message="Missing property_id parameter"
        )
        
        recovery_plan = handler.handle_error(error_context)
        
        # Mark as successful recovery (in real system, this would be based on user response)
        handler.successful_recoveries += 1
        
        # Verify metrics
        assert handler.get_recovery_success_rate() == 1.0
        assert recovery_plan.confidence > 0.5
