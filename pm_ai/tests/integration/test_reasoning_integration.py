"""
Integration tests for reasoning engine with real LLM calls.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.reasoning_engine import ReasoningEngine, ReasoningStep
from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent

@pytest.mark.integration
class TestReasoningEngineIntegration:
    """Integration tests for reasoning engine with real LLM calls."""

    def test_real_property_performance_reasoning(self, real_llm_engine, api_call_tracker):
        """Test complete reasoning chain for property performance with real LLM."""
        api_call_tracker.increment()
        api_call_tracker.increment()  # Analysis + Synthesis steps

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock property tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 85,
                "average_daily_rate": 150.0,
                "total_revenue": 12500.0,
                "bookings": 25,
                "avg_stay_length": 3.2,
                "net_revenue": 11000.0,
                "time_period": time_period
            }

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        # Create reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "original_query": "How is property P123 performing this month?"
            }
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Verify reasoning quality
        assert result_chain.completed is True
        assert result_chain.confidence > 0.5
        assert result_chain.final_result is not None
        
        # Check that analysis was performed
        analysis_node = next((node for node in result_chain.nodes if node.step_type == ReasoningStep.ANALYSIS), None)
        assert analysis_node is not None
        assert analysis_node.completed is True
        assert len(analysis_node.data_outputs.get("analysis_result", "")) > 50

        # Check synthesis
        synthesis_node = next((node for node in result_chain.nodes if node.step_type == ReasoningStep.SYNTHESIS), None)
        assert synthesis_node is not None
        assert synthesis_node.completed is True
        assert len(synthesis_node.data_outputs.get("synthesis_result", "")) > 100

    def test_real_recommendations_reasoning_chain(self, real_llm_engine, api_call_tracker):
        """Test complete reasoning chain for recommendations with real LLM."""
        api_call_tracker.increment()
        api_call_tracker.increment()
        api_call_tracker.increment()  # Analysis + Inference + Synthesis + Validation

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock tools for recommendations
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 75,  # Lower occupancy for recommendations
                "average_daily_rate": 120.0,
                "total_revenue": 9000.0,
                "market_position": "below_average"
            }

        def mock_get_property_recommendations(property_id):
            return {
                "property_id": property_id,
                "pricing_recommendations": [
                    {
                        "recommendation": "Increase weekend rates by 15%",
                        "reasoning": "Market analysis shows demand elasticity",
                        "potential_impact": "+$800 monthly revenue"
                    }
                ],
                "operational_recommendations": [
                    {
                        "recommendation": "Improve listing photos",
                        "reasoning": "Current photos underperform market standards",
                        "potential_impact": "+10% booking conversion"
                    }
                ]
            }

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_recommendations": mock_get_property_recommendations
        }

        # Create recommendations reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={
                "property_ids": ["P456"],
                "original_query": "What recommendations do you have for improving P456?"
            }
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Verify comprehensive reasoning
        assert result_chain.completed is True
        assert result_chain.confidence > 0.6
        
        # Should have all reasoning steps for recommendations
        step_types = [node.step_type for node in result_chain.nodes if node.completed]
        assert ReasoningStep.DATA_COLLECTION in step_types
        assert ReasoningStep.ANALYSIS in step_types
        assert ReasoningStep.INFERENCE in step_types
        assert ReasoningStep.SYNTHESIS in step_types
        assert ReasoningStep.VALIDATION in step_types

        # Check inference quality
        inference_node = next((node for node in result_chain.nodes if node.step_type == ReasoningStep.INFERENCE), None)
        assert inference_node is not None
        assert inference_node.completed is True
        inference_result = inference_node.data_outputs.get("inference_result", "")
        assert len(inference_result) > 100
        assert any(term in inference_result.lower() for term in ["conclusion", "recommend", "improve"])

    def test_real_comparison_reasoning_chain(self, real_llm_engine, api_call_tracker):
        """Test reasoning chain for property comparison with real LLM."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock comparison tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 88, "revenue": 13000}

        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {
                "property_id": property_id,
                "comparison_data": {
                    "occupancy_rate": {
                        "property_value": 88,
                        "cohort_avg": 78,
                        "percentile": 85,
                        "comparison_summary": "Significantly above average"
                    },
                    "revenue": {
                        "property_value": 13000,
                        "cohort_avg": 11200,
                        "percentile": 75,
                        "comparison_summary": "Above average performance"
                    }
                }
            }

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_comparisons": mock_get_property_comparisons
        }

        # Create comparison reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={
                "property_ids": ["P789"],
                "comparison_type": "vs_cohort",
                "original_query": "How does P789 compare to similar properties?"
            }
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Verify comparison reasoning
        assert result_chain.completed is True
        assert result_chain.confidence > 0.5
        
        # Check comparison step
        comparison_node = next((node for node in result_chain.nodes if node.step_type == ReasoningStep.COMPARISON), None)
        assert comparison_node is not None
        assert comparison_node.completed is True
        
        comparison_data = comparison_node.data_outputs.get("comparison_data", {})
        assert "get_property_comparisons" in comparison_data

    def test_real_reasoning_error_handling(self, real_llm_engine, api_call_tracker):
        """Test reasoning engine error handling with real LLM."""
        api_call_tracker.increment()

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock failing tool
        def mock_failing_tool(property_id):
            raise ValueError(f"Property {property_id} not found")

        available_tools = {"get_property_metrics": mock_failing_tool}

        # Create reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P999"]}
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Should handle errors gracefully
        assert "error" in result_chain.final_result
        assert result_chain.confidence == 0.0
        assert engine.get_failed_chains() == 1

    def test_real_multi_step_inference_quality(self, real_llm_engine, api_call_tracker):
        """Test quality of multi-step inference with real LLM."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock tools with rich data for inference
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 92,  # Very high
                "average_daily_rate": 180.0,  # Premium pricing
                "total_revenue": 16500.0,  # High revenue
                "guest_satisfaction": 4.8,  # Excellent reviews
                "repeat_bookings": 35,  # High loyalty
                "seasonal_trends": "peak_summer_performance"
            }

        def mock_get_outlier_statistics(property_id):
            return {
                "property_id": property_id,
                "outlier_metrics": {
                    "occupancy_rate": {
                        "detected": True,
                        "explanation": "Occupancy rate 92% is 2.5 standard deviations above market average",
                        "significance": "highly_significant"
                    },
                    "revenue_per_night": {
                        "detected": True,
                        "explanation": "Revenue per night significantly exceeds market expectations",
                        "significance": "significant"
                    }
                }
            }

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_outlier_statistics": mock_get_outlier_statistics
        }

        # Create outlier analysis reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.OUTLIER_ANALYSIS,
            confidence=0.9,
            parameters={
                "property_ids": ["P555"],
                "original_query": "Are there any unusual patterns in P555's performance?"
            }
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Verify high-quality inference
        assert result_chain.completed is True
        assert result_chain.confidence > 0.7
        
        # Check inference quality
        inference_node = next((node for node in result_chain.nodes if node.step_type == ReasoningStep.INFERENCE), None)
        if inference_node and inference_node.completed:
            inference_result = inference_node.data_outputs.get("inference_result", "")
            assert len(inference_result) > 150
            # Should identify the exceptional performance
            assert any(term in inference_result.lower() for term in ["exceptional", "outstanding", "above", "high"])

    @pytest.mark.slow
    def test_real_complex_reasoning_chain_performance(self, real_llm_engine, api_call_tracker):
        """Test performance of complex reasoning chain (marked as slow)."""
        api_call_tracker.increment()
        api_call_tracker.increment()
        api_call_tracker.increment()

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock comprehensive tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "comprehensive_data": "large_dataset"}

        def mock_get_property_recommendations(property_id):
            return {"property_id": property_id, "recommendations": "detailed_recommendations"}

        available_tools = {
            "get_property_metrics": mock_get_property_metrics,
            "get_property_recommendations": mock_get_property_recommendations
        }

        # Create complex reasoning chain
        parsed_intent = ParsedIntent(
            intent=QueryIntent.RECOMMENDATIONS,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Should complete efficiently
        assert result_chain.completed is True
        assert len(result_chain.nodes) == 5  # All reasoning steps
        assert all(node.completed for node in result_chain.nodes)

    def test_real_reasoning_chain_confidence_scoring(self, real_llm_engine, api_call_tracker):
        """Test confidence scoring accuracy with real LLM."""
        api_call_tracker.increment()

        engine = ReasoningEngine(real_llm_engine, test_mode=True)
        
        # Mock high-quality data
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 85,
                "revenue": 12000,
                "data_quality": "high",
                "sample_size": "large"
            }

        available_tools = {"get_property_metrics": mock_get_property_metrics}

        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.95,  # High input confidence
            parameters={"property_ids": ["P123"]}
        )

        chain = engine.create_reasoning_chain(parsed_intent)
        result_chain = engine.execute_reasoning_chain(chain, available_tools)

        # Should have high confidence with good data
        assert result_chain.confidence > 0.7
        assert engine.get_success_rate() == 1.0
