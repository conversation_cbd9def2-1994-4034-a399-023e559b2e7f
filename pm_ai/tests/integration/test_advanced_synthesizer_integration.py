"""
Integration tests for advanced synthesizer with real LLM calls.
"""
import pytest
from unittest.mock import Mock

from pm_ai.intelligence.advanced_synthesizer import (
    AdvancedSynthesizer, SynthesisContext, SynthesisMode, ResponseSection
)
from pm_ai.intelligence.reasoning_engine import <PERSON><PERSON><PERSON><PERSON><PERSON>, ReasoningNode, ReasoningStep
from pm_ai.intelligence.query_planner import QueryPlan, ExecutableTask, TaskType
from pm_ai.intelligence.intent_parser import QueryIntent
from pm_ai.integration.tool_orchestrator import ToolResult

@pytest.mark.integration
class TestAdvancedSynthesizerIntegration:
    """Integration tests for advanced synthesizer with real LLM calls."""

    def test_real_analytical_synthesis(self, real_llm_engine, api_call_tracker):
        """Test analytical synthesis with real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create realistic tool results
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P123",
                "occupancy_rate": 85,
                "average_daily_rate": 150.0,
                "total_revenue": 12500.0,
                "bookings": 25,
                "guest_satisfaction": 4.7
            }, 0.5),
            <PERSON><PERSON><PERSON><PERSON><PERSON>("get_property_comparisons", True, {
                "property_id": "P123",
                "comparison_data": {
                    "occupancy_rate": {
                        "property_value": 85,
                        "cohort_avg": 78,
                        "percentile": 75
                    }
                }
            }, 1.0)
        ]
        
        context = SynthesisContext(
            query="How is property P123 performing compared to similar properties?",
            intent="property_comparison",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.ANALYTICAL
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Verify analytical quality
        assert isinstance(response, str)
        assert len(response) > 100  # Should be substantial
        assert any(term in response.lower() for term in ["p123", "property", "performance"])
        assert any(term in response.lower() for term in ["85", "occupancy", "revenue"])
        assert synthesizer.get_synthesis_count() == 1

    def test_real_executive_synthesis(self, real_llm_engine, api_call_tracker):
        """Test executive synthesis mode with real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create comprehensive data for executive summary
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P456",
                "occupancy_rate": 92,
                "average_daily_rate": 180.0,
                "total_revenue": 16500.0,
                "net_profit": 12000.0,
                "year_over_year_growth": 15
            }, 0.8)
        ]
        
        context = SynthesisContext(
            query="Give me an executive summary of P456 performance",
            intent="property_performance",
            confidence=0.95,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.EXECUTIVE,
            required_sections=[
                ResponseSection.EXECUTIVE_SUMMARY,
                ResponseSection.KEY_FINDINGS,
                ResponseSection.RECOMMENDATIONS
            ]
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Verify executive format quality
        assert len(response) > 150
        assert any(term in response.lower() for term in ["summary", "executive", "key"])
        assert any(term in response.lower() for term in ["92", "occupancy", "revenue"])
        # Should be structured for executives
        assert any(term in response for term in ["•", "-", "1.", "2."] or "\n" in response)

    def test_real_complex_synthesis_with_reasoning(self, real_llm_engine, api_call_tracker):
        """Test complex synthesis with reasoning chain and real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create reasoning chain with multiple completed steps
        chain = ReasoningChain("chain_1", "Comprehensive analysis of P789", QueryIntent.RECOMMENDATIONS)
        
        # Data collection step
        data_node = ReasoningNode("step_1", ReasoningStep.DATA_COLLECTION, "Collect property data")
        data_node.completed = True
        data_node.data_outputs = {
            "collected_data": {
                "get_property_metrics": {
                    "occupancy_rate": 78,
                    "average_daily_rate": 140.0,
                    "guest_satisfaction": 4.2,
                    "repeat_bookings": 25
                }
            }
        }
        
        # Analysis step
        analysis_node = ReasoningNode("step_2", ReasoningStep.ANALYSIS, "Analyze performance patterns")
        analysis_node.completed = True
        analysis_node.data_outputs = {
            "analysis_result": "Property shows moderate performance with room for improvement in guest satisfaction and pricing optimization."
        }
        
        # Inference step
        inference_node = ReasoningNode("step_3", ReasoningStep.INFERENCE, "Draw conclusions")
        inference_node.completed = True
        inference_node.data_outputs = {
            "inference_result": "Guest satisfaction below market average suggests operational improvements needed. Pricing has potential for optimization."
        }
        
        # Synthesis step
        synthesis_node = ReasoningNode("step_4", ReasoningStep.SYNTHESIS, "Synthesize recommendations")
        synthesis_node.completed = True
        synthesis_node.data_outputs = {
            "synthesis_result": "Recommend focusing on guest experience improvements and implementing dynamic pricing strategy."
        }
        
        chain.nodes = [data_node, analysis_node, inference_node, synthesis_node]
        chain.completed = True
        chain.confidence = 0.85
        
        context = SynthesisContext(
            query="What are your recommendations for improving P789?",
            intent="recommendations",
            confidence=0.85,
            reasoning_chain=chain,
            synthesis_mode=SynthesisMode.ANALYTICAL,
            required_sections=[
                ResponseSection.KEY_FINDINGS,
                ResponseSection.RECOMMENDATIONS,
                ResponseSection.NEXT_STEPS
            ]
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Verify complex synthesis quality
        assert len(response) > 200  # Should be comprehensive
        assert any(term in response.lower() for term in ["p789", "recommend", "improve"])
        assert any(term in response.lower() for term in ["satisfaction", "pricing", "guest"])
        assert synthesizer.get_complex_synthesis_count() == 1

    def test_real_narrative_synthesis(self, real_llm_engine, api_call_tracker):
        """Test narrative synthesis mode with real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create story-worthy data
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P555",
                "occupancy_rate": 95,
                "average_daily_rate": 200.0,
                "total_revenue": 19000.0,
                "seasonal_trend": "peak_summer",
                "booking_lead_time": 45,
                "guest_demographics": "families_and_couples"
            }, 0.7)
        ]
        
        context = SynthesisContext(
            query="Tell me the story of how P555 is performing this season",
            intent="property_performance",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.NARRATIVE
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Verify narrative quality
        assert len(response) > 100
        assert any(term in response.lower() for term in ["p555", "story", "season", "performing"])
        # Should have narrative elements
        assert any(term in response.lower() for term in ["this", "your", "property", "guests"])

    def test_real_technical_synthesis(self, real_llm_engine, api_call_tracker):
        """Test technical synthesis mode with real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create technical data
        tool_results = [
            ToolResult("get_outlier_statistics", True, {
                "property_id": "P888",
                "statistical_analysis": {
                    "occupancy_rate": {
                        "value": 88,
                        "z_score": 2.1,
                        "p_value": 0.036,
                        "confidence_interval": [82, 94],
                        "outlier_detected": True
                    },
                    "revenue_per_night": {
                        "value": 165,
                        "standard_deviation": 25,
                        "coefficient_of_variation": 0.15
                    }
                }
            }, 1.2)
        ]
        
        context = SynthesisContext(
            query="Provide technical analysis of P888 statistical performance",
            intent="outlier_analysis",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.TECHNICAL
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Verify technical quality
        assert len(response) > 150
        assert any(term in response.lower() for term in ["p888", "statistical", "analysis"])
        assert any(term in response.lower() for term in ["z_score", "confidence", "deviation"] or 
                  term in response for term in ["2.1", "88", "165"])

    def test_real_confidence_based_adjustments(self, real_llm_engine, api_call_tracker):
        """Test confidence-based response adjustments with real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Low confidence scenario
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P999",
                "occupancy_rate": 75,
                "data_quality": "limited",
                "sample_size": "small"
            }, 0.3)
        ]
        
        context = SynthesisContext(
            query="How is P999 performing?",
            intent="property_performance",
            confidence=0.4,  # Low confidence
            tool_results=tool_results
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Should include confidence disclaimers
        assert any(term in response.lower() for term in ["limited", "note", "additional", "consider"])
        assert len(response) > 50

    def test_real_multi_source_synthesis(self, real_llm_engine, api_call_tracker):
        """Test synthesis with multiple data sources and real LLM."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Multiple tool results
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P777",
                "occupancy_rate": 82,
                "revenue": 14000
            }, 0.5),
            ToolResult("get_property_comparisons", True, {
                "property_id": "P777",
                "vs_market": "above_average"
            }, 0.8),
            ToolResult("get_property_recommendations", True, {
                "property_id": "P777",
                "recommendations": ["optimize_pricing", "improve_photos"]
            }, 1.0)
        ]
        
        # Query plan
        plan = QueryPlan("plan_1", "Comprehensive P777 analysis", QueryIntent.RECOMMENDATIONS)
        task1 = ExecutableTask("task_1", TaskType.DATA_RETRIEVAL, "Get metrics")
        task1.completed = True
        task1.result = {"metrics": "retrieved"}
        plan.tasks = [task1]
        plan.completed = True
        plan.complexity_score = 7.5
        
        context = SynthesisContext(
            query="Give me a complete analysis of P777 with recommendations",
            intent="recommendations",
            confidence=0.9,
            tool_results=tool_results,
            query_plan=plan,
            synthesis_mode=SynthesisMode.ANALYTICAL
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Should synthesize multiple sources
        assert len(response) > 200
        assert any(term in response.lower() for term in ["p777", "analysis", "recommend"])
        assert any(term in response.lower() for term in ["82", "occupancy", "above"])

    def test_real_error_recovery_synthesis(self, real_llm_engine, api_call_tracker):
        """Test error recovery in synthesis with real LLM."""
        # This test uses the error handling path, so no API call increment needed
        
        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create context that will trigger an error
        context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.9
        )
        
        # Mock LLM to raise exception
        original_create_completion = real_llm_engine.create_completion
        real_llm_engine.create_completion = Mock(side_effect=Exception("Network error"))
        
        try:
            response = synthesizer.synthesize_advanced_response(context)
            
            # Should handle error gracefully
            assert "error" in response.lower() or "apologize" in response.lower()
            assert synthesizer.get_error_count() == 1
        finally:
            # Restore original method
            real_llm_engine.create_completion = original_create_completion

    @pytest.mark.slow
    def test_real_comprehensive_synthesis_performance(self, real_llm_engine, api_call_tracker):
        """Test performance of comprehensive synthesis (marked as slow)."""
        api_call_tracker.increment()

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        # Create comprehensive context
        tool_results = [
            ToolResult("get_property_metrics", True, {"comprehensive": "data"}, 0.5),
            ToolResult("get_property_comparisons", True, {"comparison": "data"}, 0.8),
            ToolResult("get_property_recommendations", True, {"recommendations": "data"}, 1.0),
            ToolResult("get_outlier_statistics", True, {"outliers": "data"}, 1.2)
        ]
        
        # Complex reasoning chain
        chain = ReasoningChain("complex_chain", "Complex query", QueryIntent.RECOMMENDATIONS)
        for i in range(5):
            node = ReasoningNode(f"step_{i}", ReasoningStep.ANALYSIS, f"Step {i}")
            node.completed = True
            node.data_outputs = {f"result_{i}": f"data_{i}"}
            chain.nodes.append(node)
        chain.completed = True
        chain.confidence = 0.9
        
        context = SynthesisContext(
            query="Comprehensive analysis with all insights",
            intent="recommendations",
            confidence=0.9,
            tool_results=tool_results,
            reasoning_chain=chain,
            synthesis_mode=SynthesisMode.ANALYTICAL,
            required_sections=[
                ResponseSection.EXECUTIVE_SUMMARY,
                ResponseSection.KEY_FINDINGS,
                ResponseSection.DETAILED_ANALYSIS,
                ResponseSection.RECOMMENDATIONS,
                ResponseSection.NEXT_STEPS
            ]
        )
        
        response = synthesizer.synthesize_advanced_response(context)
        
        # Should handle complexity efficiently
        assert len(response) > 300  # Should be comprehensive
        assert synthesizer.get_complex_synthesis_count() == 1

    def test_real_synthesis_mode_quality_differences(self, real_llm_engine, api_call_tracker):
        """Test quality differences between synthesis modes with real LLM."""
        api_call_tracker.increment()
        api_call_tracker.increment()  # Two different modes

        synthesizer = AdvancedSynthesizer(real_llm_engine, test_mode=True)
        
        tool_results = [
            ToolResult("get_property_metrics", True, {
                "property_id": "P123",
                "occupancy_rate": 85,
                "revenue": 12000
            }, 0.5)
        ]
        
        # Simple mode
        simple_context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.SIMPLE
        )
        
        # Analytical mode
        analytical_context = SynthesisContext(
            query="How is P123?",
            intent="property_performance",
            confidence=0.9,
            tool_results=tool_results,
            synthesis_mode=SynthesisMode.ANALYTICAL
        )
        
        simple_response = synthesizer.synthesize_advanced_response(simple_context)
        analytical_response = synthesizer.synthesize_advanced_response(analytical_context)
        
        # Analytical should be more comprehensive
        assert len(analytical_response) >= len(simple_response)
        assert synthesizer.get_synthesis_count() == 2
