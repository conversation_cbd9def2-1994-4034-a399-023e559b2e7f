"""
Integration tests for LLM-powered agent with real LLM calls.
"""
import pytest
from unittest.mock import Mock

from pm_ai.agents import Agent, Runner

@pytest.mark.integration
class TestLLMAgentIntegration:
    """Integration tests for LLM-powered agent with real LLM calls."""

    def test_real_agent_property_performance_query(self, api_call_tracker):
        """Test agent with real LLM for property performance query."""
        api_call_tracker.increment()
        api_call_tracker.increment()  # Intent parsing + response synthesis

        # Mock property management tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {
                "property_id": property_id,
                "occupancy_rate": 85,
                "average_daily_rate": 150.0,
                "total_revenue": 12500.0,
                "bookings": 25,
                "avg_stay_length": 3.2,
                "net_revenue": 11000.0
            }

        tools = [mock_get_property_metrics]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Execute query
        result = Runner.run_sync(agent, "How is property P123 performing this month?")

        # Verify response quality
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 50  # Should be substantial
        # Should mention the property and some metrics
        response = result["final_output"].lower()
        assert any(term in response for term in ["p123", "property", "performance"])

    def test_real_agent_comparison_query(self, api_call_tracker):
        """Test agent with real LLM for property comparison query."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        # Mock comparison tools
        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {
                "property_id": property_id,
                "comparison_data": {
                    "occupancy_rate": {
                        "property_value": 85,
                        "cohort_avg": 78,
                        "percentile": 75,
                        "comparison_summary": "Above average"
                    }
                }
            }

        def mock_get_cohort_properties(cohort="similar", property_id=None):
            return {"cohort": cohort, "properties": ["P100", "P101", "P102"]}

        tools = [mock_get_property_comparisons, mock_get_cohort_properties]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Execute query
        result = Runner.run_sync(agent, "Compare P456 to similar properties")

        # Verify response quality
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 50
        response = result["final_output"].lower()
        assert any(term in response for term in ["p456", "comparison", "compare"])

    def test_real_agent_recommendations_query(self, api_call_tracker):
        """Test agent with real LLM for recommendations query."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        # Mock recommendations tool
        def mock_get_property_recommendations(property_id):
            return {
                "property_id": property_id,
                "pricing_recommendations": [
                    {
                        "recommendation": "Increase weekend rates by 10%",
                        "reasoning": "High demand on weekends",
                        "potential_impact": "+$500 monthly revenue"
                    }
                ]
            }

        tools = [mock_get_property_recommendations]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Execute query
        result = Runner.run_sync(agent, "What recommendations do you have for P789?")

        # Verify response quality
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 50
        response = result["final_output"].lower()
        assert any(term in response for term in ["p789", "recommend", "improve"])

    def test_real_agent_natural_language_variations(self, api_call_tracker):
        """Test agent with various natural language query styles."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        # Mock tool
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 90, "revenue": 15000}

        tools = [mock_get_property_metrics]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Test various query styles
        queries = [
            "How's my beachfront place doing?",
            "What's the performance of P123?",
            "Tell me about property P456 metrics",
            "P789 performance please"
        ]

        for query in queries:
            result = Runner.run_sync(agent, query)
            assert isinstance(result["final_output"], str)
            assert len(result["final_output"]) > 30  # Should provide meaningful response

    def test_real_agent_conversation_context(self, api_call_tracker):
        """Test agent conversation context management."""
        api_call_tracker.increment()
        api_call_tracker.increment()
        api_call_tracker.increment()  # Multiple interactions

        # Mock tool
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 88, "revenue": 13000}

        tools = [mock_get_property_metrics]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # First interaction
        result1 = Runner.run_sync(agent, "How is property P123 performing?")
        assert isinstance(result1["final_output"], str)

        # Follow-up interaction with pronoun reference
        result2 = Runner.run_sync(agent, "What about compared to last year?")
        assert isinstance(result2["final_output"], str)

        # Should maintain context across interactions
        assert len(result2["final_output"]) > 30

    def test_real_agent_error_recovery(self, api_call_tracker):
        """Test agent error recovery with real LLM."""
        api_call_tracker.increment()

        # Mock failing tool
        def mock_failing_tool(property_id):
            raise ValueError(f"Property {property_id} not found")

        tools = [Mock(__name__="get_property_metrics", side_effect=mock_failing_tool)]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Execute query that will cause tool failure
        result = Runner.run_sync(agent, "How is P999 performing?")

        # Should get helpful error response
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 30
        response = result["final_output"].lower()
        assert any(term in response for term in ["error", "sorry", "apologize", "issue"])

    def test_real_agent_ambiguous_query_handling(self, api_call_tracker):
        """Test agent handling of ambiguous queries."""
        api_call_tracker.increment()

        # Mock tool
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 85}

        tools = [mock_get_property_metrics]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Test ambiguous query
        result = Runner.run_sync(agent, "Tell me about stuff")

        # Should ask for clarification or provide helpful guidance
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 30
        response = result["final_output"].lower()
        assert any(term in response for term in ["specific", "clarify", "help", "property"])

    @pytest.mark.slow
    def test_real_agent_complex_multi_step_query(self, api_call_tracker):
        """Test agent with complex multi-step query (marked as slow)."""
        api_call_tracker.increment()
        api_call_tracker.increment()
        api_call_tracker.increment()

        # Mock multiple tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 85, "revenue": 12000}

        def mock_get_property_comparisons(property_id, comparison_type="vs_cohort", time_period="last_30_days"):
            return {
                "property_id": property_id,
                "comparison_data": {"occupancy_rate": {"property_value": 85, "cohort_avg": 78}}
            }

        def mock_get_property_recommendations(property_id):
            return {
                "property_id": property_id,
                "pricing_recommendations": [{"recommendation": "Optimize pricing", "reasoning": "Market analysis"}]
            }

        tools = [mock_get_property_metrics, mock_get_property_comparisons, mock_get_property_recommendations]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Complex query that might trigger multiple tools
        result = Runner.run_sync(agent, "Give me a comprehensive analysis of P123 including performance, comparisons, and recommendations")

        # Should provide comprehensive response
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 100  # Should be detailed
        response = result["final_output"].lower()
        assert "p123" in response

    def test_real_agent_tool_selection_accuracy(self, api_call_tracker):
        """Test that agent selects appropriate tools for different intents."""
        api_call_tracker.increment()

        # Mock all tools
        def mock_get_property_metrics(property_id, time_period="last_30_days", metrics=None):
            return {"property_id": property_id, "occupancy_rate": 85}

        def mock_get_outlier_statistics(property_id):
            return {"property_id": property_id, "outliers_detected": False}

        tools = [mock_get_property_metrics, mock_get_outlier_statistics]
        agent = Agent("property_ai", "Property management assistant", tools, test_mode=True)

        # Test outlier-specific query
        result = Runner.run_sync(agent, "Are there any outliers for property P123?")

        # Should use outlier tool and provide relevant response
        assert isinstance(result["final_output"], str)
        assert len(result["final_output"]) > 30
