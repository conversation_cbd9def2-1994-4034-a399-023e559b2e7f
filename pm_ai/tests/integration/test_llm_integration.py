"""
Integration tests using real OpenAI API with cost controls.
"""
import pytest
import os

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig

@pytest.mark.integration
class TestLLMIntegration:
    """Integration tests using real OpenAI API."""

    def test_real_api_completion(self, api_call_tracker):
        """Test actual API completion with cost tracking."""
        api_call_tracker.increment()

        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config)

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say 'Hello, World!' and nothing else."}
        ]

        result = engine.create_completion(messages)

        assert result["content"] is not None
        assert "hello" in result["content"].lower()
        assert result["usage"]["total_tokens"] > 0
        assert engine.get_api_call_count() == 1

    def test_function_calling_real_api(self, api_call_tracker):
        """Test function calling with real API."""
        api_call_tracker.increment()

        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config)

        messages = [{"role": "user", "content": "Extract intent from: How is P123 performing?"}]
        functions = [{
            "name": "extract_intent",
            "description": "Extract intent from property query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {"type": "string"},
                    "property_id": {"type": "string"}
                }
            }
        }]

        result = engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Should get a function call response
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    def test_token_counting_real_api(self, api_call_tracker):
        """Test token counting with real API."""
        api_call_tracker.increment()

        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config)

        text = "Hello world test message"
        count = engine.count_tokens(text)

        # Should get accurate token count
        assert count > 0
        assert isinstance(count, int)
        # Should be more accurate than fallback estimation
        assert count != int(len(text.split()) * 1.3)

    @pytest.mark.slow
    def test_retry_logic_real_api(self):
        """Test retry logic with real API (marked as slow)."""
        # This test may be slow due to retry delays
        # Only run in comprehensive test suites
        pass

    def test_api_call_counting(self, api_call_tracker):
        """Test API call counting for cost control."""
        api_call_tracker.increment()
        api_call_tracker.increment()

        config = LLMConfig(test_mode=True)
        engine = LLMEngine(config=config)

        messages = [{"role": "user", "content": "Test 1"}]
        engine.create_completion(messages)
        
        messages = [{"role": "user", "content": "Test 2"}]
        engine.create_completion(messages)

        # Should track both API calls
        assert engine.get_api_call_count() == 2
