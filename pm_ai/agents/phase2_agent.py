"""
Phase 2 Advanced Property Manager AI Agent.
Integrates multi-step reasoning, query planning, advanced synthesis, and error handling.
"""
from typing import Dict, List, Optional, Any, Callable
import logging

from ..intelligence.llm_engine import LLMEngine
from ..intelligence.intent_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ParsedIntent
from ..intelligence.reasoning_engine import <PERSON>ing<PERSON><PERSON><PERSON>, Reasoning<PERSON><PERSON>n
from ..intelligence.query_planner import Query<PERSON>lanner, QueryPlan
from ..intelligence.advanced_synthesizer import AdvancedSynthesizer, SynthesisContext, SynthesisMode
from ..intelligence.error_handler import ContextA<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>rrorContext, ErrorType, ErrorSeverity
from ..intelligence.context_manager import ContextManager
from ..conversation.session_manager import SessionManager
from ..integration.tool_orchestrator import ToolOrchestrator

class Phase2Agent:
    """
    Advanced Property Manager AI Agent with autonomous reasoning capabilities.
    
    Phase 2 Features:
    - Multi-step reasoning for complex queries
    - Autonomous query planning and decomposition
    - Advanced response synthesis with multiple modes
    - Context-aware error handling and recovery
    - Integrated conversation management
    """
    
    def __init__(self, name: str, instructions: str, tools: List[Callable], test_mode: bool = False):
        self.name = name
        self.instructions = instructions
        self.tools = tools
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        
        # Initialize LLM engine
        from ..intelligence.llm_engine import LLMConfig
        config = LLMConfig(test_mode=test_mode)
        self.llm_engine = LLMEngine(config=config)
        
        # Initialize Phase 2 intelligence modules
        self.intent_parser = IntentParser(self.llm_engine)
        self.reasoning_engine = ReasoningEngine(self.llm_engine, test_mode=test_mode)
        self.query_planner = QueryPlanner(self.llm_engine, test_mode=test_mode)
        self.advanced_synthesizer = AdvancedSynthesizer(self.llm_engine, test_mode=test_mode)
        self.error_handler = ContextAwareErrorHandler(self.llm_engine, test_mode=test_mode)

        # Initialize conversation management
        self.session_manager = SessionManager()
        self.context_manager = ContextManager(self.session_manager, test_mode=test_mode)

        # Initialize tool orchestration
        self.tool_orchestrator = ToolOrchestrator(self.llm_engine)
        
        # Create tool mapping
        self.available_tools = {tool.__name__: tool for tool in tools}
        
        if test_mode:
            self.logger.debug(f"Initialized Phase 2 Agent '{name}' with {len(tools)} tools")
    
    def process_query(self, query: str, user_id: str = "default_user") -> Dict[str, Any]:
        """
        Process a query using Phase 2 advanced reasoning capabilities.
        
        Args:
            query: User query string
            user_id: User identifier for session management
            
        Returns:
            Dictionary with response and metadata
        """
        try:
            if self.test_mode:
                self.logger.debug(f"Processing query: '{query}' for user: {user_id}")
            
            # Step 1: Get conversation context
            self.session_manager.get_or_create_session(user_id)
            conversation_context = self.session_manager.build_conversation_context()
            
            # Step 2: Enrich query with context
            enriched_context = self.context_manager.enrich_query(query, conversation_context)
            enriched_query = enriched_context["enriched_query"]
            
            # Step 3: Parse intent
            parsed_intent = self.intent_parser.parse_query(enriched_query, conversation_context)
            
            # Step 4: Determine processing approach based on complexity
            if self._requires_advanced_reasoning(parsed_intent, query):
                return self._process_complex_query(query, parsed_intent, conversation_context, user_id)
            else:
                return self._process_simple_query(query, parsed_intent, conversation_context, user_id)
                
        except Exception as e:
            return self._handle_processing_error(query, str(e), user_id)
    
    def _requires_advanced_reasoning(self, parsed_intent: ParsedIntent, query: str) -> bool:
        """Determine if query requires advanced reasoning capabilities."""
        complexity_indicators = 0
        
        # Check for multiple entities
        if len(parsed_intent.parameters.get("property_ids", [])) > 1:
            complexity_indicators += 1
        
        # Check for complex intent types
        if parsed_intent.intent.value in ["recommendations", "outlier_analysis"]:
            complexity_indicators += 1
        
        # Check for complex query language
        complex_keywords = ["comprehensive", "detailed", "analysis", "compare", "recommend", "why", "how"]
        if any(keyword in query.lower() for keyword in complex_keywords):
            complexity_indicators += 1
        
        # Check for low confidence requiring reasoning
        if parsed_intent.confidence < 0.7:
            complexity_indicators += 1
        
        return complexity_indicators >= 2
    
    def _process_complex_query(self, query: str, parsed_intent: ParsedIntent, 
                              conversation_context: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Process complex query using advanced reasoning and planning."""
        try:
            if self.test_mode:
                self.logger.debug("Processing complex query with advanced reasoning")
            
            # Create reasoning chain
            reasoning_chain = self.reasoning_engine.create_reasoning_chain(parsed_intent, conversation_context)
            
            # Create query plan
            query_plan = self.query_planner.create_query_plan(parsed_intent, conversation_context)
            
            # Execute reasoning chain
            completed_chain = self.reasoning_engine.execute_reasoning_chain(reasoning_chain, self.available_tools)
            
            # Execute query plan
            completed_plan = self.query_planner.execute_query_plan(query_plan, self.available_tools)
            
            # Advanced synthesis
            synthesis_context = SynthesisContext(
                query=query,
                intent=parsed_intent.intent.value,
                confidence=parsed_intent.confidence,
                reasoning_chain=completed_chain,
                query_plan=completed_plan,
                conversation_context=conversation_context,
                synthesis_mode=self._determine_synthesis_mode(parsed_intent, query)
            )
            
            response = self.advanced_synthesizer.synthesize_advanced_response(synthesis_context)
            
            # Update conversation context
            self.session_manager.add_interaction(
                user_id,
                query,
                parsed_intent.__dict__,
                response,
                [result.tool_name for result in completed_chain.nodes if hasattr(result, 'tool_name')]
            )
            
            return {
                "response": response,
                "reasoning_chain_id": completed_chain.chain_id,
                "query_plan_id": completed_plan.plan_id,
                "confidence": min(completed_chain.confidence, completed_plan.confidence),
                "synthesis_mode": synthesis_context.synthesis_mode.value,
                "complexity": "high"
            }
            
        except Exception as e:
            return self._handle_reasoning_error(query, parsed_intent, str(e), user_id)
    
    def _process_simple_query(self, query: str, parsed_intent: ParsedIntent, 
                             conversation_context: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Process simple query using standard tool orchestration."""
        try:
            if self.test_mode:
                self.logger.debug("Processing simple query with standard orchestration")
            
            # Execute tools
            tool_results = self.tool_orchestrator.execute_tools(parsed_intent, self.available_tools)
            
            # Simple synthesis
            synthesis_context = SynthesisContext(
                query=query,
                intent=parsed_intent.intent.value,
                confidence=parsed_intent.confidence,
                tool_results=tool_results,
                conversation_context=conversation_context,
                synthesis_mode=SynthesisMode.SIMPLE
            )
            
            response = self.advanced_synthesizer.synthesize_advanced_response(synthesis_context)
            
            # Update conversation context
            self.session_manager.add_interaction(
                user_id,
                query,
                parsed_intent.__dict__,
                response,
                [result.tool_name for result in tool_results if result.success]
            )
            
            return {
                "response": response,
                "confidence": parsed_intent.confidence,
                "tools_used": [result.tool_name for result in tool_results if result.success],
                "complexity": "simple"
            }
            
        except Exception as e:
            return self._handle_tool_error(query, parsed_intent, str(e), user_id)
    
    def _determine_synthesis_mode(self, parsed_intent: ParsedIntent, query: str) -> SynthesisMode:
        """Determine appropriate synthesis mode based on query characteristics."""
        query_lower = query.lower()
        
        if "executive" in query_lower or "summary" in query_lower:
            return SynthesisMode.EXECUTIVE
        elif "technical" in query_lower or "detailed" in query_lower:
            return SynthesisMode.TECHNICAL
        elif "story" in query_lower or "narrative" in query_lower:
            return SynthesisMode.NARRATIVE
        elif parsed_intent.intent.value in ["recommendations", "outlier_analysis"]:
            return SynthesisMode.ANALYTICAL
        else:
            return SynthesisMode.SIMPLE
    
    def _handle_processing_error(self, query: str, error_message: str, user_id: str) -> Dict[str, Any]:
        """Handle general processing errors."""
        error_context = ErrorContext(
            error_type=ErrorType.SYSTEM_ERROR,
            severity=ErrorSeverity.HIGH,
            original_query=query,
            error_message=error_message,
            conversation_history=self.session_manager.build_conversation_context().get("history", [])
        )
        
        recovery_plan = self.error_handler.handle_error(error_context)
        
        return {
            "response": recovery_plan.clarification_message,
            "error": True,
            "recovery_strategy": recovery_plan.strategy.value,
            "confidence": recovery_plan.confidence
        }
    
    def _handle_reasoning_error(self, query: str, parsed_intent: ParsedIntent, 
                               error_message: str, user_id: str) -> Dict[str, Any]:
        """Handle reasoning-specific errors."""
        error_context = ErrorContext(
            error_type=ErrorType.SYSTEM_ERROR,
            severity=ErrorSeverity.HIGH,
            original_query=query,
            parsed_intent=parsed_intent,
            error_message=f"Reasoning error: {error_message}",
            conversation_history=self.session_manager.build_conversation_context().get("history", [])
        )
        
        recovery_plan = self.error_handler.handle_error(error_context)
        
        return {
            "response": recovery_plan.clarification_message,
            "error": True,
            "recovery_strategy": recovery_plan.strategy.value,
            "confidence": recovery_plan.confidence,
            "fallback_to_simple": True
        }
    
    def _handle_tool_error(self, query: str, parsed_intent: ParsedIntent, 
                          error_message: str, user_id: str) -> Dict[str, Any]:
        """Handle tool execution errors."""
        error_context = ErrorContext(
            error_type=ErrorType.TOOL_EXECUTION_ERROR,
            severity=ErrorSeverity.MEDIUM,
            original_query=query,
            parsed_intent=parsed_intent,
            error_message=error_message,
            conversation_history=self.session_manager.build_conversation_context().get("history", [])
        )
        
        recovery_plan = self.error_handler.handle_error(error_context)
        
        return {
            "response": recovery_plan.clarification_message,
            "error": True,
            "recovery_strategy": recovery_plan.strategy.value,
            "confidence": recovery_plan.confidence,
            "suggested_queries": recovery_plan.suggested_queries
        }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get comprehensive agent status and metrics."""
        return {
            "name": self.name,
            "phase": "2",
            "capabilities": [
                "multi_step_reasoning",
                "autonomous_query_planning", 
                "advanced_synthesis",
                "context_aware_error_handling"
            ],
            "tools_available": len(self.available_tools),
            "metrics": {
                "reasoning_chains_executed": self.reasoning_engine.get_chains_executed(),
                "query_plans_created": self.query_planner.get_plans_created(),
                "synthesis_operations": self.advanced_synthesizer.get_synthesis_count(),
                "errors_handled": self.error_handler.get_errors_handled(),
                "conversations_active": len(self.session_manager.active_sessions)
            }
        }
    
    def reset_metrics(self):
        """Reset all agent metrics (for testing)."""
        self.reasoning_engine.reset_metrics()
        self.query_planner.reset_metrics()
        self.advanced_synthesizer.reset_metrics()
        self.error_handler.reset_metrics()
        self.context_manager.reset_counters()
        self.session_manager.reset_counters()
        self.tool_orchestrator.reset_counters()
        
        if self.test_mode:
            self.logger.debug("Reset all Phase 2 agent metrics")
