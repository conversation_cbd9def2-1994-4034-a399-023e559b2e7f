"""
LLM-powered Agent and Runner implementations for PM_AI.
Replaces hardcoded pattern matching with intelligent natural language understanding.
"""
import logging
from typing import List, Callable, Optional, TypedDict

from ..intelligence.llm_engine import LLMEngine, LLMConfig
from ..intelligence.intent_parser import IntentParser
from ..intelligence.context_manager import Context<PERSON>anager
from ..conversation.session_manager import SessionManager
from ..conversation.memory_store import MemoryStore
from ..integration.tool_orchestrator import ToolOrchestrator
from ..integration.result_synthesizer import ResultSynthesizer

class CompletionResult(TypedDict):
    """Result from a tool completion."""
    final_output: str

class Agent:
    """
    LLM-powered Agent for PM_<PERSON> with natural language understanding.

    Replaces hardcoded regex patterns with intelligent intent recognition,
    context management, and dynamic tool orchestration.
    """
    def __init__(self, name: str, instructions: str, tools: List[Callable], test_mode: bool = False):
        self.name = name
        self.instructions = instructions
        self.tools = tools
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

        # Initialize LLM-powered intelligence modules
        self._initialize_intelligence_modules()

        # Create tool mapping for orchestrator
        self.available_tools = {tool.__name__: tool for tool in tools}

    def _initialize_intelligence_modules(self):
        """Initialize all intelligence modules."""
        # Initialize LLM engine
        config = LLMConfig(test_mode=self.test_mode)
        self.llm_engine = LLMEngine(config=config)

        # Initialize conversation management
        memory_store = MemoryStore(test_mode=self.test_mode)
        self.session_manager = SessionManager(memory_store, test_mode=self.test_mode)

        # Initialize intelligence modules
        self.intent_parser = IntentParser(self.llm_engine)
        self.context_manager = ContextManager(self.session_manager, test_mode=self.test_mode)

        # Initialize tool orchestration
        self.tool_orchestrator = ToolOrchestrator(self.llm_engine, test_mode=self.test_mode)
        self.result_synthesizer = ResultSynthesizer(self.llm_engine, test_mode=self.test_mode)

        if self.test_mode:
            self.logger.debug(f"Initialized LLM-powered agent '{self.name}' in test mode")

    def get_tool_by_name(self, tool_name: str) -> Optional[Callable]:
        """Get a tool function by its name."""
        return self.available_tools.get(tool_name)

class Runner:
    """
    LLM-powered Runner for executing Agent interactions.

    Replaces hardcoded pattern matching with intelligent natural language
    understanding, context management, and dynamic tool orchestration.
    """
    @staticmethod
    def run_sync(agent: Agent, query: str) -> CompletionResult:
        """
        Process a query through the LLM-powered agent and return the result.

        Uses natural language understanding to parse intent, manage context,
        orchestrate tools, and synthesize responses.
        """
        try:
            # Step 1: Enrich query with conversation context
            enriched_context = agent.context_manager.enrich_query(query)
            enriched_query = enriched_context["enriched_query"]
            conversation_context = enriched_context["context"]

            if agent.test_mode:
                agent.logger.debug(f"Processing query: '{query}' -> '{enriched_query}'")

            # Step 2: Parse intent from enriched query
            parsed_intent = agent.intent_parser.parse_query(enriched_query, conversation_context)

            # Step 3: Select and execute appropriate tools
            tool_results = agent.tool_orchestrator.execute_tools(parsed_intent, agent.available_tools)

            # Step 4: Synthesize natural language response
            response = agent.result_synthesizer.synthesize_response(
                query,
                parsed_intent.__dict__,
                tool_results,
                conversation_context
            )

            # Step 5: Update conversation context
            agent.session_manager.add_interaction(
                query,
                parsed_intent.__dict__,
                response,
                [result.tool_name for result in tool_results if result.success]
            )

            if agent.test_mode:
                agent.logger.debug(f"Generated response: {response[:100]}...")

            return {"final_output": response}

        except Exception as e:
            error_message = f"I apologize, but I encountered an error while processing your request: {str(e)}"
            agent.logger.error(f"Error in run_sync: {str(e)}")

            # Still try to update conversation context with error
            try:
                agent.session_manager.add_interaction(
                    query,
                    {"intent": "error", "error": str(e)},
                    error_message,
                    []
                )
            except:
                pass  # Don't let context update errors compound the original error

            return {"final_output": error_message}