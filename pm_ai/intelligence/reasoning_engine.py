"""
Multi-step reasoning engine for complex property management queries.
Supports autonomous reasoning chains, logical inference, and data synthesis.
"""
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import json

from .llm_engine import LLMEngine
from .intent_parser import ParsedIntent, QueryIntent

class ReasoningStep(Enum):
    """Types of reasoning steps."""
    DATA_COLLECTION = "data_collection"
    ANALYSIS = "analysis"
    COMPARISON = "comparison"
    INFERENCE = "inference"
    SYNTHESIS = "synthesis"
    VALIDATION = "validation"

@dataclass
class ReasoningNode:
    """A single step in the reasoning chain."""
    step_id: str
    step_type: ReasoningStep
    description: str
    dependencies: List[str] = field(default_factory=list)
    tools_required: List[str] = field(default_factory=list)
    data_inputs: Dict[str, Any] = field(default_factory=dict)
    data_outputs: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    completed: bool = False
    error_message: Optional[str] = None

@dataclass
class ReasoningChain:
    """Complete reasoning chain for a complex query."""
    chain_id: str
    query: str
    intent: QueryIntent
    nodes: List[ReasoningNode] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    final_result: Optional[Dict[str, Any]] = None
    confidence: float = 0.0
    completed: bool = False

class ReasoningEngine:
    """
    Multi-step reasoning engine for complex property management queries.
    
    Capabilities:
    - Decomposes complex queries into logical reasoning steps
    - Executes reasoning chains with dependency management
    - Synthesizes results from multiple reasoning paths
    - Provides confidence scoring and validation
    """
    
    def __init__(self, llm_engine: LLMEngine, test_mode: bool = False):
        self.llm_engine = llm_engine
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        
        # Reasoning patterns for different query types
        self.reasoning_patterns = self._initialize_reasoning_patterns()
        
        # Execution metrics
        self.chains_executed = 0
        self.successful_chains = 0
        self.failed_chains = 0
        
    def _initialize_reasoning_patterns(self) -> Dict[QueryIntent, List[ReasoningStep]]:
        """Initialize reasoning patterns for different query types."""
        return {
            QueryIntent.PROPERTY_PERFORMANCE: [
                ReasoningStep.DATA_COLLECTION,
                ReasoningStep.ANALYSIS,
                ReasoningStep.SYNTHESIS
            ],
            QueryIntent.PROPERTY_COMPARISON: [
                ReasoningStep.DATA_COLLECTION,
                ReasoningStep.COMPARISON,
                ReasoningStep.ANALYSIS,
                ReasoningStep.SYNTHESIS
            ],
            QueryIntent.RECOMMENDATIONS: [
                ReasoningStep.DATA_COLLECTION,
                ReasoningStep.ANALYSIS,
                ReasoningStep.INFERENCE,
                ReasoningStep.SYNTHESIS,
                ReasoningStep.VALIDATION
            ],
            QueryIntent.OUTLIER_ANALYSIS: [
                ReasoningStep.DATA_COLLECTION,
                ReasoningStep.ANALYSIS,
                ReasoningStep.COMPARISON,
                ReasoningStep.INFERENCE,
                ReasoningStep.VALIDATION
            ],
            QueryIntent.COHORT_ANALYSIS: [
                ReasoningStep.DATA_COLLECTION,
                ReasoningStep.COMPARISON,
                ReasoningStep.ANALYSIS,
                ReasoningStep.SYNTHESIS
            ]
        }
    
    def create_reasoning_chain(self, parsed_intent: ParsedIntent, context: Dict[str, Any] = None) -> ReasoningChain:
        """
        Create a reasoning chain for a complex query.
        
        Args:
            parsed_intent: Parsed user intent
            context: Optional conversation context
            
        Returns:
            ReasoningChain with planned execution steps
        """
        self.chains_executed += 1
        
        chain_id = f"chain_{self.chains_executed}"
        chain = ReasoningChain(
            chain_id=chain_id,
            query=parsed_intent.parameters.get("original_query", ""),
            intent=parsed_intent.intent
        )
        
        # Get reasoning pattern for this intent
        pattern = self.reasoning_patterns.get(parsed_intent.intent, [ReasoningStep.DATA_COLLECTION, ReasoningStep.SYNTHESIS])
        
        # Create reasoning nodes
        for i, step_type in enumerate(pattern):
            node = self._create_reasoning_node(f"{chain_id}_step_{i}", step_type, parsed_intent, context)
            chain.nodes.append(node)
            chain.execution_order.append(node.step_id)
        
        # Set dependencies
        self._set_node_dependencies(chain)
        
        if self.test_mode:
            self.logger.debug(f"Created reasoning chain {chain_id} with {len(chain.nodes)} steps")
        
        return chain
    
    def _create_reasoning_node(self, step_id: str, step_type: ReasoningStep, 
                              parsed_intent: ParsedIntent, context: Dict[str, Any] = None) -> ReasoningNode:
        """Create a reasoning node for a specific step type."""
        node = ReasoningNode(
            step_id=step_id,
            step_type=step_type,
            description=self._get_step_description(step_type, parsed_intent),
            tools_required=self._get_required_tools(step_type, parsed_intent),
            data_inputs=self._get_step_inputs(step_type, parsed_intent, context)
        )
        return node
    
    def _get_step_description(self, step_type: ReasoningStep, parsed_intent: ParsedIntent) -> str:
        """Get human-readable description for a reasoning step."""
        descriptions = {
            ReasoningStep.DATA_COLLECTION: f"Collect relevant data for {parsed_intent.intent.value}",
            ReasoningStep.ANALYSIS: f"Analyze collected data for patterns and insights",
            ReasoningStep.COMPARISON: f"Compare data against benchmarks and cohorts",
            ReasoningStep.INFERENCE: f"Draw logical conclusions from analysis",
            ReasoningStep.SYNTHESIS: f"Synthesize findings into actionable insights",
            ReasoningStep.VALIDATION: f"Validate conclusions and recommendations"
        }
        return descriptions.get(step_type, f"Execute {step_type.value} step")
    
    def _get_required_tools(self, step_type: ReasoningStep, parsed_intent: ParsedIntent) -> List[str]:
        """Get tools required for a reasoning step."""
        tool_mapping = {
            ReasoningStep.DATA_COLLECTION: {
                QueryIntent.PROPERTY_PERFORMANCE: ["get_property_metrics"],
                QueryIntent.PROPERTY_COMPARISON: ["get_property_metrics", "get_property_comparisons"],
                QueryIntent.RECOMMENDATIONS: ["get_property_metrics", "get_property_recommendations"],
                QueryIntent.OUTLIER_ANALYSIS: ["get_property_metrics", "get_outlier_statistics"],
                QueryIntent.COHORT_ANALYSIS: ["get_cohort_properties"]
            },
            ReasoningStep.COMPARISON: {
                QueryIntent.PROPERTY_COMPARISON: ["get_property_comparisons"],
                QueryIntent.OUTLIER_ANALYSIS: ["get_outlier_statistics"],
                QueryIntent.COHORT_ANALYSIS: ["get_cohort_properties"]
            }
        }
        
        intent_tools = tool_mapping.get(step_type, {})
        return intent_tools.get(parsed_intent.intent, [])
    
    def _get_step_inputs(self, step_type: ReasoningStep, parsed_intent: ParsedIntent, 
                        context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get input data for a reasoning step."""
        inputs = {
            "intent": parsed_intent.intent.value,
            "parameters": parsed_intent.parameters,
            "context": context or {}
        }
        
        if step_type == ReasoningStep.DATA_COLLECTION:
            inputs["data_requirements"] = self._identify_data_requirements(parsed_intent)
        
        return inputs
    
    def _identify_data_requirements(self, parsed_intent: ParsedIntent) -> List[str]:
        """Identify what data is needed for the query."""
        requirements = []
        
        if "property_ids" in parsed_intent.parameters:
            requirements.append("property_metrics")
        
        if parsed_intent.intent == QueryIntent.PROPERTY_COMPARISON:
            requirements.extend(["comparison_data", "cohort_data"])
        elif parsed_intent.intent == QueryIntent.RECOMMENDATIONS:
            requirements.extend(["performance_data", "market_data"])
        elif parsed_intent.intent == QueryIntent.OUTLIER_ANALYSIS:
            requirements.extend(["statistical_data", "benchmark_data"])
        
        return requirements
    
    def _set_node_dependencies(self, chain: ReasoningChain):
        """Set dependencies between reasoning nodes."""
        for i, node in enumerate(chain.nodes):
            if i > 0:
                # Each node depends on the previous one
                node.dependencies.append(chain.nodes[i-1].step_id)
            
            # Special dependency rules
            if node.step_type == ReasoningStep.SYNTHESIS:
                # Synthesis depends on all analysis steps
                for prev_node in chain.nodes[:i]:
                    if prev_node.step_type in [ReasoningStep.ANALYSIS, ReasoningStep.COMPARISON, ReasoningStep.INFERENCE]:
                        if prev_node.step_id not in node.dependencies:
                            node.dependencies.append(prev_node.step_id)
    
    # Testing utility methods
    def get_chains_executed(self) -> int:
        """Get number of reasoning chains executed (for testing)."""
        return self.chains_executed
    
    def get_successful_chains(self) -> int:
        """Get number of successful reasoning chains (for testing)."""
        return self.successful_chains
    
    def get_failed_chains(self) -> int:
        """Get number of failed reasoning chains (for testing)."""
        return self.failed_chains
    
    def get_success_rate(self) -> float:
        """Get success rate of reasoning chains (for testing)."""
        if self.chains_executed == 0:
            return 0.0
        return self.successful_chains / self.chains_executed
    
    def execute_reasoning_chain(self, chain: ReasoningChain, available_tools: Dict[str, Any]) -> ReasoningChain:
        """
        Execute a reasoning chain with dependency management.

        Args:
            chain: ReasoningChain to execute
            available_tools: Dictionary of available tool functions

        Returns:
            Updated ReasoningChain with execution results
        """
        try:
            if self.test_mode:
                self.logger.debug(f"Executing reasoning chain {chain.chain_id}")

            # Execute nodes in dependency order
            for step_id in chain.execution_order:
                node = self._get_node_by_id(chain, step_id)
                if not node:
                    continue

                # Check if dependencies are satisfied
                if not self._dependencies_satisfied(chain, node):
                    node.error_message = f"Dependencies not satisfied: {node.dependencies}"
                    continue

                # Execute the reasoning step
                self._execute_reasoning_node(node, chain, available_tools)

            # Synthesize final result
            chain.final_result = self._synthesize_chain_result(chain)
            chain.confidence = self._calculate_chain_confidence(chain)
            chain.completed = True

            self.successful_chains += 1

            if self.test_mode:
                self.logger.debug(f"Completed reasoning chain {chain.chain_id} with confidence {chain.confidence}")

        except Exception as e:
            self.failed_chains += 1
            self.logger.error(f"Failed to execute reasoning chain {chain.chain_id}: {str(e)}")
            chain.final_result = {"error": str(e)}
            chain.confidence = 0.0

        # Check if any critical steps failed and mark as error
        if chain.final_result and not isinstance(chain.final_result.get("error"), str):
            failed_steps = [node for node in chain.nodes if node.error_message or
                          (node.completed and any("failed" in str(output).lower() for output in node.data_outputs.values()))]
            if failed_steps:
                chain.final_result["error"] = f"Critical reasoning steps failed: {[node.step_id for node in failed_steps]}"
                chain.confidence = min(chain.confidence, 0.3)

        return chain

    def _get_node_by_id(self, chain: ReasoningChain, step_id: str) -> Optional[ReasoningNode]:
        """Get a reasoning node by its ID."""
        for node in chain.nodes:
            if node.step_id == step_id:
                return node
        return None

    def _dependencies_satisfied(self, chain: ReasoningChain, node: ReasoningNode) -> bool:
        """Check if all dependencies for a node are satisfied."""
        for dep_id in node.dependencies:
            dep_node = self._get_node_by_id(chain, dep_id)
            if not dep_node or not dep_node.completed:
                return False
        return True

    def _execute_reasoning_node(self, node: ReasoningNode, chain: ReasoningChain, available_tools: Dict[str, Any]):
        """Execute a single reasoning node."""
        try:
            if node.step_type == ReasoningStep.DATA_COLLECTION:
                self._execute_data_collection(node, available_tools)
            elif node.step_type == ReasoningStep.ANALYSIS:
                self._execute_analysis(node, chain)
            elif node.step_type == ReasoningStep.COMPARISON:
                self._execute_comparison(node, chain, available_tools)
            elif node.step_type == ReasoningStep.INFERENCE:
                self._execute_inference(node, chain)
            elif node.step_type == ReasoningStep.SYNTHESIS:
                self._execute_synthesis(node, chain)
            elif node.step_type == ReasoningStep.VALIDATION:
                self._execute_validation(node, chain)

            node.completed = True
            node.confidence = 0.8  # Default confidence for successful execution

        except Exception as e:
            node.error_message = str(e)
            node.confidence = 0.0
            self.logger.error(f"Failed to execute reasoning node {node.step_id}: {str(e)}")

    def _execute_data_collection(self, node: ReasoningNode, available_tools: Dict[str, Any]):
        """Execute data collection step."""
        collected_data = {}

        for tool_name in node.tools_required:
            if tool_name in available_tools:
                tool_func = available_tools[tool_name]

                # Extract parameters for tool execution
                params = self._extract_tool_parameters(tool_name, node.data_inputs)

                try:
                    result = tool_func(**params)
                    collected_data[tool_name] = result
                except Exception as e:
                    collected_data[tool_name] = {"error": str(e)}

        node.data_outputs["collected_data"] = collected_data

    def _execute_analysis(self, node: ReasoningNode, chain: ReasoningChain):
        """Execute analysis step using LLM reasoning."""
        # Gather data from previous steps
        analysis_data = self._gather_previous_data(chain, node)

        # Use LLM for analysis
        analysis_prompt = self._create_analysis_prompt(node, analysis_data)

        try:
            messages = [
                {"role": "system", "content": "You are an expert property management analyst."},
                {"role": "user", "content": analysis_prompt}
            ]

            response = self.llm_engine.create_completion(messages)
            node.data_outputs["analysis_result"] = response.get("content", "")

        except Exception as e:
            node.data_outputs["analysis_result"] = f"Analysis failed: {str(e)}"

    def _execute_comparison(self, node: ReasoningNode, chain: ReasoningChain, available_tools: Dict[str, Any]):
        """Execute comparison step."""
        # Get comparison data using tools
        comparison_data = {}

        for tool_name in node.tools_required:
            if tool_name in available_tools:
                tool_func = available_tools[tool_name]
                params = self._extract_tool_parameters(tool_name, node.data_inputs)

                try:
                    result = tool_func(**params)
                    comparison_data[tool_name] = result
                except Exception as e:
                    comparison_data[tool_name] = {"error": str(e)}

        node.data_outputs["comparison_data"] = comparison_data

    def _execute_inference(self, node: ReasoningNode, chain: ReasoningChain):
        """Execute inference step using LLM reasoning."""
        # Gather all previous analysis and comparison data
        inference_data = self._gather_previous_data(chain, node)

        # Use LLM for logical inference
        inference_prompt = self._create_inference_prompt(node, inference_data)

        try:
            messages = [
                {"role": "system", "content": "You are an expert at drawing logical conclusions from property data."},
                {"role": "user", "content": inference_prompt}
            ]

            response = self.llm_engine.create_completion(messages)
            node.data_outputs["inference_result"] = response.get("content", "")

        except Exception as e:
            node.data_outputs["inference_result"] = f"Inference failed: {str(e)}"

    def _execute_synthesis(self, node: ReasoningNode, chain: ReasoningChain):
        """Execute synthesis step to combine all findings."""
        # Gather all data from the chain
        synthesis_data = self._gather_all_chain_data(chain)

        # Use LLM for synthesis
        synthesis_prompt = self._create_synthesis_prompt(node, synthesis_data)

        try:
            messages = [
                {"role": "system", "content": "You are an expert at synthesizing property management insights."},
                {"role": "user", "content": synthesis_prompt}
            ]

            response = self.llm_engine.create_completion(messages)
            node.data_outputs["synthesis_result"] = response.get("content", "")

        except Exception as e:
            node.data_outputs["synthesis_result"] = f"Synthesis failed: {str(e)}"

    def _execute_validation(self, node: ReasoningNode, chain: ReasoningChain):
        """Execute validation step to verify conclusions."""
        # Gather synthesis results for validation
        validation_data = self._gather_previous_data(chain, node)

        # Use LLM for validation
        validation_prompt = self._create_validation_prompt(node, validation_data)

        try:
            messages = [
                {"role": "system", "content": "You are an expert at validating property management conclusions."},
                {"role": "user", "content": validation_prompt}
            ]

            response = self.llm_engine.create_completion(messages)
            node.data_outputs["validation_result"] = response.get("content", "")

        except Exception as e:
            node.data_outputs["validation_result"] = f"Validation failed: {str(e)}"

    def _extract_tool_parameters(self, tool_name: str, data_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for tool execution from data inputs."""
        params = {}
        intent_params = data_inputs.get("parameters", {})

        if "property_ids" in intent_params and intent_params["property_ids"]:
            params["property_id"] = intent_params["property_ids"][0]

        if "time_period" in intent_params:
            params["time_period"] = intent_params["time_period"]

        if "metrics" in intent_params:
            params["metrics"] = intent_params["metrics"]

        if tool_name == "get_property_comparisons":
            params["comparison_type"] = intent_params.get("comparison_type", "vs_cohort")

        return params

    def _gather_previous_data(self, chain: ReasoningChain, current_node: ReasoningNode) -> Dict[str, Any]:
        """Gather data from previous reasoning steps."""
        gathered_data = {}

        for node in chain.nodes:
            if node.step_id == current_node.step_id:
                break
            if node.completed and node.data_outputs:
                gathered_data[node.step_id] = node.data_outputs

        return gathered_data

    def _gather_all_chain_data(self, chain: ReasoningChain) -> Dict[str, Any]:
        """Gather all data from the reasoning chain."""
        all_data = {}

        for node in chain.nodes:
            if node.completed and node.data_outputs:
                all_data[node.step_id] = node.data_outputs

        return all_data

    def _create_analysis_prompt(self, node: ReasoningNode, data: Dict[str, Any]) -> str:
        """Create prompt for analysis step."""
        return f"""
        Analyze the following property data and provide insights:

        Data: {json.dumps(data, indent=2)}

        Please provide:
        1. Key patterns and trends
        2. Notable findings
        3. Areas of concern or opportunity
        4. Data quality assessment

        Focus on actionable insights for property management.
        """

    def _create_inference_prompt(self, node: ReasoningNode, data: Dict[str, Any]) -> str:
        """Create prompt for inference step."""
        return f"""
        Based on the analysis and comparison data below, draw logical conclusions:

        Data: {json.dumps(data, indent=2)}

        Please provide:
        1. Logical conclusions from the data
        2. Cause-and-effect relationships
        3. Predictive insights
        4. Risk assessments
        5. Opportunity identification

        Ensure conclusions are well-supported by the data.
        """

    def _create_synthesis_prompt(self, node: ReasoningNode, data: Dict[str, Any]) -> str:
        """Create prompt for synthesis step."""
        return f"""
        Synthesize all findings into a comprehensive property management report:

        All Data: {json.dumps(data, indent=2)}

        Please provide:
        1. Executive summary
        2. Key findings and insights
        3. Actionable recommendations
        4. Priority actions
        5. Expected outcomes

        Structure as a professional property management report.
        """

    def _create_validation_prompt(self, node: ReasoningNode, data: Dict[str, Any]) -> str:
        """Create prompt for validation step."""
        return f"""
        Validate the conclusions and recommendations below:

        Data: {json.dumps(data, indent=2)}

        Please assess:
        1. Logical consistency of conclusions
        2. Data support for recommendations
        3. Potential risks or oversights
        4. Alternative interpretations
        5. Confidence levels

        Provide validation score and any concerns.
        """

    def _synthesize_chain_result(self, chain: ReasoningChain) -> Dict[str, Any]:
        """Synthesize final result from completed reasoning chain."""
        result = {
            "chain_id": chain.chain_id,
            "query": chain.query,
            "intent": chain.intent.value,
            "steps_completed": sum(1 for node in chain.nodes if node.completed),
            "total_steps": len(chain.nodes),
            "findings": {}
        }

        # Extract key findings from each step
        for node in chain.nodes:
            if node.completed and node.data_outputs:
                result["findings"][node.step_type.value] = node.data_outputs

        # Get final synthesis if available
        synthesis_node = next((node for node in chain.nodes if node.step_type == ReasoningStep.SYNTHESIS), None)
        if synthesis_node and synthesis_node.completed:
            result["final_synthesis"] = synthesis_node.data_outputs.get("synthesis_result", "")

        return result

    def _calculate_chain_confidence(self, chain: ReasoningChain) -> float:
        """Calculate overall confidence for the reasoning chain."""
        if not chain.nodes:
            return 0.0

        completed_nodes = [node for node in chain.nodes if node.completed]
        if not completed_nodes:
            return 0.0

        # Average confidence of completed nodes
        total_confidence = sum(node.confidence for node in completed_nodes)
        avg_confidence = total_confidence / len(completed_nodes)

        # Penalty for incomplete chain
        completion_ratio = len(completed_nodes) / len(chain.nodes)

        return avg_confidence * completion_ratio

    def reset_metrics(self):
        """Reset execution metrics (for testing)."""
        self.chains_executed = 0
        self.successful_chains = 0
        self.failed_chains = 0
        if self.test_mode:
            self.logger.debug("Reset reasoning engine metrics for testing")
