"""
Context-aware error handling and recovery for Property Manager AI.
Provides intelligent error analysis, clarification requests, and recovery mechanisms.
"""
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import json

from .llm_engine import LLMEngine
from .intent_parser import ParsedIntent, QueryIntent

class ErrorType(Enum):
    """Types of errors that can occur."""
    PARSING_ERROR = "parsing_error"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    DATA_VALIDATION_ERROR = "data_validation_error"
    INSUFFICIENT_CONTEXT = "insufficient_context"
    AMBIGUOUS_QUERY = "ambiguous_query"
    MISSING_PARAMETERS = "missing_parameters"
    SYSTEM_ERROR = "system_error"
    TIMEOUT_ERROR = "timeout_error"

class ErrorSeverity(Enum):
    """Severity levels for errors."""
    LOW = "low"           # Minor issues, can continue
    MEDIUM = "medium"     # Significant issues, may need clarification
    HIGH = "high"         # Major issues, requires intervention
    CRITICAL = "critical" # System-level issues, cannot continue

class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    CLARIFY_QUERY = "clarify_query"
    SUGGEST_ALTERNATIVES = "suggest_alternatives"
    PROVIDE_EXAMPLES = "provide_examples"
    REQUEST_PARAMETERS = "request_parameters"
    FALLBACK_RESPONSE = "fallback_response"
    RETRY_WITH_DEFAULTS = "retry_with_defaults"
    ESCALATE_TO_HUMAN = "escalate_to_human"

@dataclass
class ErrorContext:
    """Context information for error analysis."""
    error_type: ErrorType
    severity: ErrorSeverity
    original_query: str
    parsed_intent: Optional[ParsedIntent] = None
    error_message: str = ""
    stack_trace: Optional[str] = None
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    user_context: Optional[Dict[str, Any]] = None
    system_state: Optional[Dict[str, Any]] = None
    recovery_attempts: int = 0

@dataclass
class RecoveryPlan:
    """Plan for error recovery."""
    strategy: RecoveryStrategy
    clarification_message: str
    suggested_queries: List[str] = field(default_factory=list)
    required_parameters: List[str] = field(default_factory=list)
    fallback_actions: List[str] = field(default_factory=list)
    confidence: float = 0.0

class ContextAwareErrorHandler:
    """
    Context-aware error handling and recovery system.
    
    Capabilities:
    - Intelligent error classification and severity assessment
    - Context-aware clarification request generation
    - Recovery strategy selection based on error type and context
    - Learning from error patterns to improve handling
    - Graceful degradation with fallback responses
    """
    
    def __init__(self, llm_engine: LLMEngine, test_mode: bool = False):
        self.llm_engine = llm_engine
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        
        # Error handling patterns
        self.error_patterns = self._initialize_error_patterns()
        self.recovery_strategies = self._initialize_recovery_strategies()
        
        # Error tracking metrics
        self.errors_handled = 0
        self.successful_recoveries = 0
        self.failed_recoveries = 0
        self.clarification_requests = 0
        
    def _initialize_error_patterns(self) -> Dict[ErrorType, Dict[str, Any]]:
        """Initialize error patterns for classification."""
        return {
            ErrorType.PARSING_ERROR: {
                "keywords": ["parse", "understand", "unclear", "ambiguous"],
                "default_severity": ErrorSeverity.MEDIUM,
                "recovery_strategies": [RecoveryStrategy.CLARIFY_QUERY, RecoveryStrategy.PROVIDE_EXAMPLES]
            },
            ErrorType.TOOL_EXECUTION_ERROR: {
                "keywords": ["not found", "failed", "error", "exception"],
                "default_severity": ErrorSeverity.HIGH,
                "recovery_strategies": [RecoveryStrategy.SUGGEST_ALTERNATIVES, RecoveryStrategy.RETRY_WITH_DEFAULTS]
            },
            ErrorType.INSUFFICIENT_CONTEXT: {
                "keywords": ["specify", "which", "more information", "unclear"],
                "default_severity": ErrorSeverity.MEDIUM,
                "recovery_strategies": [RecoveryStrategy.REQUEST_PARAMETERS, RecoveryStrategy.PROVIDE_EXAMPLES]
            },
            ErrorType.AMBIGUOUS_QUERY: {
                "keywords": ["multiple", "ambiguous", "unclear", "specify"],
                "default_severity": ErrorSeverity.MEDIUM,
                "recovery_strategies": [RecoveryStrategy.CLARIFY_QUERY, RecoveryStrategy.SUGGEST_ALTERNATIVES]
            },
            ErrorType.MISSING_PARAMETERS: {
                "keywords": ["missing", "required", "parameter", "specify"],
                "default_severity": ErrorSeverity.MEDIUM,
                "recovery_strategies": [RecoveryStrategy.REQUEST_PARAMETERS, RecoveryStrategy.PROVIDE_EXAMPLES]
            },
            ErrorType.SYSTEM_ERROR: {
                "keywords": ["system", "internal", "server", "unavailable"],
                "default_severity": ErrorSeverity.CRITICAL,
                "recovery_strategies": [RecoveryStrategy.FALLBACK_RESPONSE, RecoveryStrategy.ESCALATE_TO_HUMAN]
            }
        }
    
    def _initialize_recovery_strategies(self) -> Dict[RecoveryStrategy, str]:
        """Initialize recovery strategy templates."""
        return {
            RecoveryStrategy.CLARIFY_QUERY: """
            I need some clarification to better understand your request. Could you please:
            {clarification_points}
            """,
            
            RecoveryStrategy.SUGGEST_ALTERNATIVES: """
            I encountered an issue with your request. Here are some alternatives you might try:
            {alternatives}
            """,
            
            RecoveryStrategy.PROVIDE_EXAMPLES: """
            I'm not sure I understood your request correctly. Here are some examples of what you can ask:
            {examples}
            """,
            
            RecoveryStrategy.REQUEST_PARAMETERS: """
            To help you with this request, I need some additional information:
            {required_parameters}
            """,
            
            RecoveryStrategy.FALLBACK_RESPONSE: """
            I'm experiencing some technical difficulties right now. Here's what I can tell you based on general information:
            {fallback_info}
            """,
            
            RecoveryStrategy.RETRY_WITH_DEFAULTS: """
            Let me try a different approach with some default settings:
            {retry_explanation}
            """
        }
    
    def handle_error(self, error_context: ErrorContext) -> RecoveryPlan:
        """
        Handle an error with context-aware recovery planning.
        
        Args:
            error_context: ErrorContext with error details and context
            
        Returns:
            RecoveryPlan with recovery strategy and clarification
        """
        self.errors_handled += 1
        
        try:
            if self.test_mode:
                self.logger.debug(f"Handling error: {error_context.error_type.value}")
            
            # Classify error severity if not provided
            if not hasattr(error_context, 'severity') or error_context.severity is None:
                error_context.severity = self._classify_error_severity(error_context)
            
            # Select recovery strategy
            strategy = self._select_recovery_strategy(error_context)
            
            # Generate recovery plan
            recovery_plan = self._generate_recovery_plan(error_context, strategy)
            
            if self.test_mode:
                self.logger.debug(f"Generated recovery plan with strategy: {strategy.value}")
            
            return recovery_plan
            
        except Exception as e:
            self.failed_recoveries += 1
            self.logger.error(f"Failed to handle error: {str(e)}")
            return self._generate_fallback_recovery_plan(error_context)
    
    def _classify_error_severity(self, error_context: ErrorContext) -> ErrorSeverity:
        """Classify error severity based on context."""
        error_pattern = self.error_patterns.get(error_context.error_type, {})
        base_severity = error_pattern.get("default_severity", ErrorSeverity.MEDIUM)
        
        # Adjust severity based on context
        if error_context.recovery_attempts > 2:
            # Escalate severity after multiple failed attempts
            if base_severity == ErrorSeverity.LOW:
                return ErrorSeverity.MEDIUM
            elif base_severity == ErrorSeverity.MEDIUM:
                return ErrorSeverity.HIGH
        
        # Check for system-level indicators
        if any(keyword in error_context.error_message.lower() for keyword in ["system", "internal", "critical"]):
            return ErrorSeverity.CRITICAL
        
        return base_severity
    
    def _select_recovery_strategy(self, error_context: ErrorContext) -> RecoveryStrategy:
        """Select appropriate recovery strategy based on error context."""
        error_pattern = self.error_patterns.get(error_context.error_type, {})
        available_strategies = error_pattern.get("recovery_strategies", [RecoveryStrategy.FALLBACK_RESPONSE])
        
        # Consider conversation history
        if error_context.conversation_history:
            recent_turns = error_context.conversation_history[-3:]
            clarification_attempts = [turn for turn in recent_turns
                                    if any(keyword in turn.get("system", "").lower()
                                          for keyword in ["clarification", "specify", "could you", "need more"])]

            # If we've been asking for clarification repeatedly, try alternatives
            if len(clarification_attempts) >= 2 and RecoveryStrategy.SUGGEST_ALTERNATIVES in available_strategies:
                return RecoveryStrategy.SUGGEST_ALTERNATIVES
        
        # Consider recovery attempts
        if error_context.recovery_attempts > 1:
            # Escalate to more direct strategies
            if RecoveryStrategy.PROVIDE_EXAMPLES in available_strategies:
                return RecoveryStrategy.PROVIDE_EXAMPLES
            elif RecoveryStrategy.FALLBACK_RESPONSE in available_strategies:
                return RecoveryStrategy.FALLBACK_RESPONSE
        
        # Default to first available strategy
        return available_strategies[0] if available_strategies else RecoveryStrategy.FALLBACK_RESPONSE
    
    def _generate_recovery_plan(self, error_context: ErrorContext, strategy: RecoveryStrategy) -> RecoveryPlan:
        """Generate detailed recovery plan with LLM assistance."""
        # Build prompt for LLM-generated recovery plan
        system_prompt = self._build_recovery_system_prompt(strategy)
        user_prompt = self._build_recovery_user_prompt(error_context, strategy)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm_engine.create_completion(messages)
        
        # Parse LLM response into recovery plan
        recovery_plan = self._parse_recovery_response(response.get("content", ""), strategy)
        
        # Add context-specific enhancements
        self._enhance_recovery_plan(recovery_plan, error_context)
        
        return recovery_plan
    
    def _build_recovery_system_prompt(self, strategy: RecoveryStrategy) -> str:
        """Build system prompt for recovery plan generation."""
        base_prompt = """You are an expert at helping users recover from errors in property management queries. 
Your goal is to provide clear, helpful guidance that gets the user back on track."""
        
        strategy_specific = {
            RecoveryStrategy.CLARIFY_QUERY: "Focus on asking specific clarifying questions that will help understand the user's intent.",
            RecoveryStrategy.SUGGEST_ALTERNATIVES: "Provide concrete alternative queries or approaches the user can try.",
            RecoveryStrategy.PROVIDE_EXAMPLES: "Give clear, realistic examples of how to phrase their request.",
            RecoveryStrategy.REQUEST_PARAMETERS: "Identify the specific information needed and ask for it clearly.",
            RecoveryStrategy.FALLBACK_RESPONSE: "Provide helpful general information while acknowledging the limitation."
        }
        
        return f"{base_prompt}\n\n{strategy_specific.get(strategy, '')}"
    
    def _build_recovery_user_prompt(self, error_context: ErrorContext, strategy: RecoveryStrategy) -> str:
        """Build user prompt for recovery plan generation."""
        prompt = f"Error Type: {error_context.error_type.value}\n"
        prompt += f"Original Query: {error_context.original_query}\n"
        prompt += f"Error Message: {error_context.error_message}\n"
        prompt += f"Recovery Strategy: {strategy.value}\n\n"
        
        if error_context.parsed_intent:
            prompt += f"Parsed Intent: {error_context.parsed_intent.intent.value}\n"
            prompt += f"Intent Confidence: {error_context.parsed_intent.confidence}\n"
            prompt += f"Parameters: {error_context.parsed_intent.parameters}\n\n"
        
        if error_context.conversation_history:
            prompt += "Recent Conversation:\n"
            for turn in error_context.conversation_history[-2:]:
                prompt += f"User: {turn.get('user', '')}\n"
                prompt += f"System: {turn.get('system', '')}\n"
            prompt += "\n"
        
        prompt += "Please provide a helpful recovery response that addresses this error and guides the user toward a successful interaction."
        
        return prompt
    
    def _parse_recovery_response(self, response: str, strategy: RecoveryStrategy) -> RecoveryPlan:
        """Parse LLM response into structured recovery plan."""
        return RecoveryPlan(
            strategy=strategy,
            clarification_message=response,
            confidence=0.8  # Default confidence for LLM-generated plans
        )
    
    def _enhance_recovery_plan(self, recovery_plan: RecoveryPlan, error_context: ErrorContext):
        """Enhance recovery plan with context-specific information."""
        # Add suggested queries based on error type
        if error_context.error_type == ErrorType.AMBIGUOUS_QUERY:
            recovery_plan.suggested_queries = [
                "How is property P123 performing this month?",
                "Compare property P456 to similar properties",
                "What recommendations do you have for property P789?"
            ]
        
        # Add required parameters for missing parameter errors
        if error_context.error_type == ErrorType.MISSING_PARAMETERS:
            recovery_plan.required_parameters = ["property_id", "time_period", "metrics"]
    
    def _generate_fallback_recovery_plan(self, error_context: ErrorContext) -> RecoveryPlan:
        """Generate fallback recovery plan when error handling fails."""
        return RecoveryPlan(
            strategy=RecoveryStrategy.FALLBACK_RESPONSE,
            clarification_message=f"I apologize, but I encountered an issue processing your request '{error_context.original_query}'. Could you please try rephrasing your question or provide more specific details?",
            confidence=0.3
        )
    
    # Testing utility methods
    def get_errors_handled(self) -> int:
        """Get number of errors handled (for testing)."""
        return self.errors_handled
    
    def get_successful_recoveries(self) -> int:
        """Get number of successful recoveries (for testing)."""
        return self.successful_recoveries
    
    def get_failed_recoveries(self) -> int:
        """Get number of failed recoveries (for testing)."""
        return self.failed_recoveries
    
    def get_clarification_requests(self) -> int:
        """Get number of clarification requests (for testing)."""
        return self.clarification_requests
    
    def get_recovery_success_rate(self) -> float:
        """Get recovery success rate (for testing)."""
        if self.errors_handled == 0:
            return 0.0
        return self.successful_recoveries / self.errors_handled
    
    def reset_metrics(self):
        """Reset error handling metrics (for testing)."""
        self.errors_handled = 0
        self.successful_recoveries = 0
        self.failed_recoveries = 0
        self.clarification_requests = 0
        if self.test_mode:
            self.logger.debug("Reset error handler metrics for testing")
