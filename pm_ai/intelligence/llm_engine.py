"""
Test-aware LLM engine for OpenAI GPT-4 integration.
Supports both mocked unit tests and real API integration tests.
"""
import os
import time
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from openai import OpenAI
import tiktoken

@dataclass
class LLMConfig:
    """Configuration for LLM engine."""
    model: str = "gpt-4o-mini"
    max_tokens: int = 4096
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 30
    test_mode: bool = False  # NEW: Enable test-specific behavior

class LLMEngine:
    """
    Test-aware LLM engine for OpenAI GPT-4 integration.

    Prevents API key handling conflicts by supporting:
    - Mock clients for unit tests (no API calls)
    - Real API clients for integration tests (limited calls)
    - Clear environment separation
    """

    def __init__(self, config: Optional[LLMConfig] = None, mock_client=None):
        self.config = config or LLMConfig()

        # Test-aware client initialization
        if mock_client:
            # Use provided mock client (for unit tests)
            self.client = mock_client
            self.is_mocked = True
        elif self.config.test_mode:
            # Test mode with real API but special handling
            api_key = self._get_test_api_key()
            self.client = OpenAI(api_key=api_key) if api_key else None
            self.is_mocked = False
        else:
            # Production mode
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            self.client = OpenAI(api_key=api_key)
            self.is_mocked = False

        # Initialize encoding if we have a real client
        if self.client and not self.is_mocked:
            try:
                self.encoding = tiktoken.encoding_for_model(self.config.model)
            except Exception:
                # Fallback for test environments
                self.encoding = tiktoken.get_encoding("cl100k_base")
        else:
            self.encoding = None

        self.logger = logging.getLogger(__name__)
        self.api_call_count = 0  # Track API calls for cost control

    def _get_test_api_key(self) -> Optional[str]:
        """Get API key for testing."""
        try:
            from ..config.test_config import TestConfig
            return TestConfig.get_openai_key()
        except ImportError:
            # Fallback if test_config not available
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if self.encoding:
            return len(self.encoding.encode(text))
        else:
            # Fallback estimation for mocked tests
            return int(len(text.split()) * 1.3)  # Rough approximation
    
    def create_completion(
        self,
        messages: List[Dict[str, str]],
        functions: Optional[List[Dict]] = None,
        function_call: Optional[Union[str, Dict]] = None
    ) -> Dict[str, Any]:
        """
        Create completion with test-aware behavior.

        Args:
            messages: List of conversation messages
            functions: Available functions for function calling
            function_call: Function call specification

        Returns:
            Dict containing response and metadata
        """

        if not self.client:
            raise ValueError("No OpenAI client available (missing API key)")

        # For mocked clients, return test response
        if self.is_mocked:
            return self._create_mock_response(messages, functions, function_call)

        # Track API calls for cost control
        self.api_call_count += 1

        # Real API call logic
        for attempt in range(self.config.max_retries):
            try:
                request_params = {
                    "model": self.config.model,
                    "messages": messages,
                    "max_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature,
                    "timeout": self.config.timeout
                }

                if functions:
                    request_params["tools"] = [{"type": "function", "function": f} for f in functions]
                if function_call:
                    if isinstance(function_call, str):
                        request_params["tool_choice"] = {"type": "function", "function": {"name": function_call}}
                    else:
                        request_params["tool_choice"] = function_call

                response = self.client.chat.completions.create(**request_params)
                return self._process_response(response, messages)

            except Exception as e:
                self.logger.warning(f"LLM API attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))

    def _create_mock_response(self, messages, functions, function_call) -> Dict[str, Any]:
        """Create mock response for testing."""
        # Default mock response
        mock_response = {
            "content": "Mock response for testing",
            "function_call": None,
            "finish_reason": "stop",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        }

        # If function calling is expected, create mock function call
        if functions and function_call:
            if isinstance(function_call, dict) and "name" in function_call:
                function_name = function_call["name"]
            elif isinstance(function_call, str):
                function_name = function_call
            else:
                function_name = functions[0]["name"] if functions else "mock_function"

            mock_response["function_call"] = {
                "name": function_name,
                "arguments": {"mock": "response", "confidence": 0.95}
            }
            mock_response["content"] = None

        return mock_response

    def _process_response(self, response, messages: List[Dict]) -> Dict[str, Any]:
        """Process OpenAI API response and extract relevant information."""
        # Use messages parameter to avoid unused parameter warning
        _ = messages
        choice = response.choices[0]
        message = choice.message

        result = {
            "content": message.content,
            "function_call": None,
            "finish_reason": choice.finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

        # Handle tool calls (new OpenAI API format)
        if hasattr(message, 'tool_calls') and message.tool_calls:
            tool_call = message.tool_calls[0]
            if tool_call.type == "function":
                result["function_call"] = {
                    "name": tool_call.function.name,
                    "arguments": json.loads(tool_call.function.arguments)
                }

        return result

    def get_api_call_count(self) -> int:
        """Get number of API calls made by this engine instance."""
        return self.api_call_count
