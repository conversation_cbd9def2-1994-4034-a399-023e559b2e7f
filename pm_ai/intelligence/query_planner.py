"""
Autonomous query planning system for complex property management queries.
Decomposes complex requests into executable sub-tasks and manages execution flow.
"""
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
import json

from .llm_engine import LLMEngine
from .intent_parser import ParsedIntent, QueryIntent

class TaskType(Enum):
    """Types of executable tasks."""
    DATA_RETRIEVAL = "data_retrieval"
    ANALYSIS = "analysis"
    COMPARISON = "comparison"
    CALCULATION = "calculation"
    SYNTHESIS = "synthesis"
    VALIDATION = "validation"

class TaskPriority(Enum):
    """Task execution priorities."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class ExecutableTask:
    """A single executable task in the query plan."""
    task_id: str
    task_type: TaskType
    description: str
    tool_name: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    priority: TaskPriority = TaskPriority.MEDIUM
    estimated_duration: float = 1.0  # seconds
    required_data: List[str] = field(default_factory=list)
    output_data: List[str] = field(default_factory=list)
    completed: bool = False
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

@dataclass
class QueryPlan:
    """Complete execution plan for a complex query."""
    plan_id: str
    original_query: str
    intent: QueryIntent
    tasks: List[ExecutableTask] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    estimated_total_duration: float = 0.0
    complexity_score: float = 0.0
    confidence: float = 0.0
    completed: bool = False
    final_result: Optional[Dict[str, Any]] = None

class QueryPlanner:
    """
    Autonomous query planning system for complex property management queries.
    
    Capabilities:
    - Decomposes complex queries into executable sub-tasks
    - Optimizes task execution order based on dependencies
    - Estimates execution time and complexity
    - Manages parallel execution opportunities
    - Provides fallback planning for failed tasks
    """
    
    def __init__(self, llm_engine: LLMEngine, test_mode: bool = False):
        self.llm_engine = llm_engine
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        
        # Planning patterns for different query types
        self.planning_patterns = self._initialize_planning_patterns()
        
        # Available tools mapping
        self.tool_capabilities = self._initialize_tool_capabilities()
        
        # Execution metrics
        self.plans_created = 0
        self.successful_plans = 0
        self.failed_plans = 0
        
    def _initialize_planning_patterns(self) -> Dict[QueryIntent, List[TaskType]]:
        """Initialize planning patterns for different query types."""
        return {
            QueryIntent.PROPERTY_PERFORMANCE: [
                TaskType.DATA_RETRIEVAL,
                TaskType.ANALYSIS,
                TaskType.SYNTHESIS
            ],
            QueryIntent.PROPERTY_COMPARISON: [
                TaskType.DATA_RETRIEVAL,
                TaskType.DATA_RETRIEVAL,  # Multiple properties
                TaskType.COMPARISON,
                TaskType.ANALYSIS,
                TaskType.SYNTHESIS
            ],
            QueryIntent.RECOMMENDATIONS: [
                TaskType.DATA_RETRIEVAL,
                TaskType.ANALYSIS,
                TaskType.CALCULATION,
                TaskType.SYNTHESIS,
                TaskType.VALIDATION
            ],
            QueryIntent.OUTLIER_ANALYSIS: [
                TaskType.DATA_RETRIEVAL,
                TaskType.CALCULATION,
                TaskType.ANALYSIS,
                TaskType.VALIDATION
            ],
            QueryIntent.COHORT_ANALYSIS: [
                TaskType.DATA_RETRIEVAL,
                TaskType.COMPARISON,
                TaskType.ANALYSIS,
                TaskType.SYNTHESIS
            ]
        }
    
    def _initialize_tool_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Initialize tool capabilities mapping."""
        return {
            "get_property_metrics": {
                "task_types": [TaskType.DATA_RETRIEVAL],
                "output_data": ["property_metrics", "performance_data"],
                "estimated_duration": 0.5,
                "complexity": 1
            },
            "get_property_comparisons": {
                "task_types": [TaskType.DATA_RETRIEVAL, TaskType.COMPARISON],
                "output_data": ["comparison_data", "benchmark_data"],
                "estimated_duration": 1.0,
                "complexity": 2
            },
            "get_property_recommendations": {
                "task_types": [TaskType.DATA_RETRIEVAL, TaskType.ANALYSIS],
                "output_data": ["recommendations", "insights"],
                "estimated_duration": 1.5,
                "complexity": 3
            },
            "get_outlier_statistics": {
                "task_types": [TaskType.CALCULATION, TaskType.ANALYSIS],
                "output_data": ["statistical_data", "outlier_analysis"],
                "estimated_duration": 1.2,
                "complexity": 2
            },
            "get_cohort_properties": {
                "task_types": [TaskType.DATA_RETRIEVAL],
                "output_data": ["cohort_data", "group_metrics"],
                "estimated_duration": 0.8,
                "complexity": 1
            }
        }
    
    def create_query_plan(self, parsed_intent: ParsedIntent, context: Dict[str, Any] = None) -> QueryPlan:
        """
        Create an execution plan for a complex query.
        
        Args:
            parsed_intent: Parsed user intent
            context: Optional conversation context
            
        Returns:
            QueryPlan with executable tasks and dependencies
        """
        self.plans_created += 1
        
        plan_id = f"plan_{self.plans_created}"
        plan = QueryPlan(
            plan_id=plan_id,
            original_query=parsed_intent.parameters.get("original_query", ""),
            intent=parsed_intent.intent
        )
        
        # Decompose query into tasks
        tasks = self._decompose_query(parsed_intent, context)
        plan.tasks = tasks
        
        # Optimize execution order
        plan.execution_order = self._optimize_execution_order(tasks)
        
        # Calculate estimates
        plan.estimated_total_duration = self._estimate_total_duration(tasks)
        plan.complexity_score = self._calculate_complexity_score(tasks)
        plan.confidence = self._calculate_plan_confidence(parsed_intent, tasks)
        
        if self.test_mode:
            self.logger.debug(f"Created query plan {plan_id} with {len(tasks)} tasks")
        
        return plan
    
    def _decompose_query(self, parsed_intent: ParsedIntent, context: Dict[str, Any] = None) -> List[ExecutableTask]:
        """Decompose query into executable tasks."""
        tasks = []
        
        # Get base pattern for this intent
        pattern = self.planning_patterns.get(parsed_intent.intent, [TaskType.DATA_RETRIEVAL, TaskType.SYNTHESIS])
        
        # Create tasks based on pattern
        for i, task_type in enumerate(pattern):
            task = self._create_task(f"task_{i+1}", task_type, parsed_intent, context)
            tasks.append(task)
        
        # Set task dependencies
        self._set_task_dependencies(tasks)
        
        # Optimize for specific query characteristics
        tasks = self._optimize_for_query_specifics(tasks, parsed_intent)
        
        return tasks
    
    def _create_task(self, task_id: str, task_type: TaskType, 
                    parsed_intent: ParsedIntent, context: Dict[str, Any] = None) -> ExecutableTask:
        """Create a single executable task."""
        task = ExecutableTask(
            task_id=task_id,
            task_type=task_type,
            description=self._get_task_description(task_type, parsed_intent),
            priority=self._determine_task_priority(task_type, parsed_intent),
            parameters=self._extract_task_parameters(task_type, parsed_intent, context)
        )
        
        # Assign tool if applicable
        tool_name = self._select_tool_for_task(task_type, parsed_intent)
        if tool_name:
            task.tool_name = tool_name
            tool_info = self.tool_capabilities.get(tool_name, {})
            task.estimated_duration = tool_info.get("estimated_duration", 1.0)
            task.output_data = tool_info.get("output_data", [])
        
        return task
    
    def _get_task_description(self, task_type: TaskType, parsed_intent: ParsedIntent) -> str:
        """Get human-readable description for a task."""
        descriptions = {
            TaskType.DATA_RETRIEVAL: f"Retrieve data for {parsed_intent.intent.value}",
            TaskType.ANALYSIS: f"Analyze data for insights and patterns",
            TaskType.COMPARISON: f"Compare data against benchmarks",
            TaskType.CALCULATION: f"Perform calculations and statistical analysis",
            TaskType.SYNTHESIS: f"Synthesize findings into actionable insights",
            TaskType.VALIDATION: f"Validate conclusions and recommendations"
        }
        return descriptions.get(task_type, f"Execute {task_type.value} task")
    
    def _determine_task_priority(self, task_type: TaskType, parsed_intent: ParsedIntent) -> TaskPriority:
        """Determine priority for a task."""
        # Data retrieval is always critical
        if task_type == TaskType.DATA_RETRIEVAL:
            return TaskPriority.CRITICAL
        
        # Synthesis is high priority for final results
        if task_type == TaskType.SYNTHESIS:
            return TaskPriority.HIGH
        
        # Analysis is high priority for recommendations
        if task_type == TaskType.ANALYSIS and parsed_intent.intent == QueryIntent.RECOMMENDATIONS:
            return TaskPriority.HIGH
        
        return TaskPriority.MEDIUM
    
    def _extract_task_parameters(self, task_type: TaskType, parsed_intent: ParsedIntent, 
                                context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for task execution."""
        params = {
            "intent": parsed_intent.intent.value,
            "confidence": parsed_intent.confidence
        }
        
        # Add intent-specific parameters
        if "property_ids" in parsed_intent.parameters:
            params["property_ids"] = parsed_intent.parameters["property_ids"]
        
        if "time_period" in parsed_intent.parameters:
            params["time_period"] = parsed_intent.parameters["time_period"]
        
        if "metrics" in parsed_intent.parameters:
            params["metrics"] = parsed_intent.parameters["metrics"]
        
        # Add context if available
        if context:
            params["context"] = context
        
        return params
    
    def _select_tool_for_task(self, task_type: TaskType, parsed_intent: ParsedIntent) -> Optional[str]:
        """Select appropriate tool for a task."""
        # Map task types to tools based on intent
        tool_mapping = {
            (TaskType.DATA_RETRIEVAL, QueryIntent.PROPERTY_PERFORMANCE): "get_property_metrics",
            (TaskType.DATA_RETRIEVAL, QueryIntent.PROPERTY_COMPARISON): "get_property_metrics",
            (TaskType.COMPARISON, QueryIntent.PROPERTY_COMPARISON): "get_property_comparisons",
            (TaskType.DATA_RETRIEVAL, QueryIntent.RECOMMENDATIONS): "get_property_recommendations",
            (TaskType.CALCULATION, QueryIntent.OUTLIER_ANALYSIS): "get_outlier_statistics",
            (TaskType.DATA_RETRIEVAL, QueryIntent.COHORT_ANALYSIS): "get_cohort_properties"
        }
        
        return tool_mapping.get((task_type, parsed_intent.intent))
    
    def _set_task_dependencies(self, tasks: List[ExecutableTask]):
        """Set dependencies between tasks."""
        for i, task in enumerate(tasks):
            if i > 0:
                # Each task depends on the previous one by default
                task.dependencies.append(tasks[i-1].task_id)
            
            # Special dependency rules
            if task.task_type == TaskType.SYNTHESIS:
                # Synthesis depends on all analysis and comparison tasks
                for prev_task in tasks[:i]:
                    if prev_task.task_type in [TaskType.ANALYSIS, TaskType.COMPARISON, TaskType.CALCULATION]:
                        if prev_task.task_id not in task.dependencies:
                            task.dependencies.append(prev_task.task_id)
    
    def _optimize_execution_order(self, tasks: List[ExecutableTask]) -> List[str]:
        """Optimize task execution order based on dependencies and priorities."""
        # Topological sort with priority consideration
        ordered_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # Find tasks with no unmet dependencies
            ready_tasks = [
                task for task in remaining_tasks
                if all(dep_id in [t.task_id for t in ordered_tasks] for dep_id in task.dependencies)
            ]
            
            if not ready_tasks:
                # Circular dependency or error - add remaining tasks
                ready_tasks = remaining_tasks
            
            # Sort by priority and add to execution order
            ready_tasks.sort(key=lambda t: (t.priority.value, t.estimated_duration))
            
            for task in ready_tasks:
                ordered_tasks.append(task)
                remaining_tasks.remove(task)
        
        return [task.task_id for task in ordered_tasks]
    
    def _optimize_for_query_specifics(self, tasks: List[ExecutableTask], 
                                    parsed_intent: ParsedIntent) -> List[ExecutableTask]:
        """Optimize tasks for specific query characteristics."""
        # Add parallel execution opportunities
        if len(parsed_intent.parameters.get("property_ids", [])) > 1:
            # Multiple properties can be processed in parallel
            self._add_parallel_data_retrieval(tasks, parsed_intent)
        
        # Add validation for high-stakes queries
        if parsed_intent.intent == QueryIntent.RECOMMENDATIONS and parsed_intent.confidence > 0.8:
            validation_task = ExecutableTask(
                task_id=f"validation_{len(tasks)+1}",
                task_type=TaskType.VALIDATION,
                description="Validate recommendations",
                priority=TaskPriority.HIGH,
                dependencies=[task.task_id for task in tasks if task.task_type == TaskType.SYNTHESIS]
            )
            tasks.append(validation_task)
        
        return tasks
    
    def _add_parallel_data_retrieval(self, tasks: List[ExecutableTask], parsed_intent: ParsedIntent):
        """Add parallel data retrieval tasks for multiple properties."""
        property_ids = parsed_intent.parameters.get("property_ids", [])
        if len(property_ids) <= 1:
            return
        
        # Find data retrieval tasks
        data_tasks = [task for task in tasks if task.task_type == TaskType.DATA_RETRIEVAL]
        
        for i, property_id in enumerate(property_ids[1:], 1):  # Skip first property
            for data_task in data_tasks:
                parallel_task = ExecutableTask(
                    task_id=f"{data_task.task_id}_parallel_{i}",
                    task_type=data_task.task_type,
                    description=f"{data_task.description} for {property_id}",
                    tool_name=data_task.tool_name,
                    parameters={**data_task.parameters, "property_ids": [property_id]},
                    priority=data_task.priority,
                    estimated_duration=data_task.estimated_duration
                )
                tasks.append(parallel_task)
    
    # Utility methods for testing and metrics
    def get_plans_created(self) -> int:
        """Get number of plans created (for testing)."""
        return self.plans_created
    
    def get_successful_plans(self) -> int:
        """Get number of successful plans (for testing)."""
        return self.successful_plans
    
    def get_failed_plans(self) -> int:
        """Get number of failed plans (for testing)."""
        return self.failed_plans
    
    def _estimate_total_duration(self, tasks: List[ExecutableTask]) -> float:
        """Estimate total execution duration for all tasks."""
        # Consider parallel execution opportunities
        critical_path_duration = 0.0
        parallel_groups = self._identify_parallel_groups(tasks)

        for group in parallel_groups:
            group_duration = max(task.estimated_duration for task in group)
            critical_path_duration += group_duration

        return critical_path_duration

    def _identify_parallel_groups(self, tasks: List[ExecutableTask]) -> List[List[ExecutableTask]]:
        """Identify groups of tasks that can be executed in parallel."""
        groups = []
        remaining_tasks = tasks.copy()
        completed_task_ids = set()

        while remaining_tasks:
            # Find tasks with no unmet dependencies
            parallel_group = []
            for task in remaining_tasks.copy():
                unmet_deps = [dep for dep in task.dependencies if dep not in completed_task_ids]
                if not unmet_deps:
                    parallel_group.append(task)
                    remaining_tasks.remove(task)

            if parallel_group:
                groups.append(parallel_group)
                # Mark these tasks as completed for dependency checking
                completed_task_ids.update(task.task_id for task in parallel_group)
            else:
                # Fallback: add remaining tasks sequentially
                groups.extend([[task] for task in remaining_tasks])
                break

        return groups

    def _calculate_complexity_score(self, tasks: List[ExecutableTask]) -> float:
        """Calculate complexity score for the query plan."""
        base_complexity = len(tasks)

        # Add complexity for different task types
        type_complexity = {
            TaskType.DATA_RETRIEVAL: 1,
            TaskType.ANALYSIS: 2,
            TaskType.COMPARISON: 2,
            TaskType.CALCULATION: 3,
            TaskType.SYNTHESIS: 2,
            TaskType.VALIDATION: 1
        }

        total_complexity = sum(type_complexity.get(task.task_type, 1) for task in tasks)

        # Add complexity for dependencies
        dependency_complexity = sum(len(task.dependencies) for task in tasks) * 0.5

        return base_complexity + total_complexity + dependency_complexity

    def _calculate_plan_confidence(self, parsed_intent: ParsedIntent, tasks: List[ExecutableTask]) -> float:
        """Calculate confidence score for the query plan."""
        base_confidence = parsed_intent.confidence

        # Reduce confidence for complex plans
        complexity_penalty = min(0.2, len(tasks) * 0.02)

        # Reduce confidence for tasks without assigned tools
        unassigned_tasks = [task for task in tasks if not task.tool_name and task.task_type == TaskType.DATA_RETRIEVAL]
        tool_penalty = len(unassigned_tasks) * 0.1

        # Boost confidence for well-structured plans
        structure_bonus = 0.1 if self._has_good_structure(tasks) else 0.0

        final_confidence = base_confidence - complexity_penalty - tool_penalty + structure_bonus
        return max(0.0, min(1.0, final_confidence))

    def _has_good_structure(self, tasks: List[ExecutableTask]) -> bool:
        """Check if the plan has good structure."""
        # Good structure: starts with data retrieval, ends with synthesis
        if not tasks:
            return False

        has_data_retrieval = any(task.task_type == TaskType.DATA_RETRIEVAL for task in tasks)
        has_synthesis = any(task.task_type == TaskType.SYNTHESIS for task in tasks)

        return has_data_retrieval and has_synthesis

    def execute_query_plan(self, plan: QueryPlan, available_tools: Dict[str, Any]) -> QueryPlan:
        """
        Execute a query plan with task orchestration.

        Args:
            plan: QueryPlan to execute
            available_tools: Dictionary of available tool functions

        Returns:
            Updated QueryPlan with execution results
        """
        try:
            if self.test_mode:
                self.logger.debug(f"Executing query plan {plan.plan_id}")

            # Execute tasks in order
            for task_id in plan.execution_order:
                task = self._get_task_by_id(plan, task_id)
                if not task:
                    continue

                # Check if dependencies are satisfied
                if not self._task_dependencies_satisfied(plan, task):
                    task.error_message = f"Dependencies not satisfied: {task.dependencies}"
                    continue

                # Execute the task
                self._execute_task(task, plan, available_tools)

            # Synthesize final result
            plan.final_result = self._synthesize_plan_result(plan)
            plan.completed = True

            self.successful_plans += 1

            if self.test_mode:
                self.logger.debug(f"Completed query plan {plan.plan_id}")

        except Exception as e:
            self.failed_plans += 1
            self.logger.error(f"Failed to execute query plan {plan.plan_id}: {str(e)}")
            plan.final_result = {"error": str(e)}

        return plan

    def _get_task_by_id(self, plan: QueryPlan, task_id: str) -> Optional[ExecutableTask]:
        """Get a task by its ID."""
        for task in plan.tasks:
            if task.task_id == task_id:
                return task
        return None

    def _task_dependencies_satisfied(self, plan: QueryPlan, task: ExecutableTask) -> bool:
        """Check if all dependencies for a task are satisfied."""
        for dep_id in task.dependencies:
            dep_task = self._get_task_by_id(plan, dep_id)
            if not dep_task or not dep_task.completed:
                return False
        return True

    def _execute_task(self, task: ExecutableTask, plan: QueryPlan, available_tools: Dict[str, Any]):
        """Execute a single task."""
        try:
            if task.tool_name and task.tool_name in available_tools:
                # Execute tool-based task
                tool_func = available_tools[task.tool_name]

                # Extract parameters for tool execution
                tool_params = self._extract_tool_parameters(task)

                result = tool_func(**tool_params)
                task.result = result
                task.completed = True

            else:
                # Execute non-tool task (analysis, synthesis, etc.)
                task.result = {"message": f"Executed {task.task_type.value} task"}
                task.completed = True

        except Exception as e:
            task.error_message = str(e)
            self.logger.error(f"Failed to execute task {task.task_id}: {str(e)}")

    def _extract_tool_parameters(self, task: ExecutableTask) -> Dict[str, Any]:
        """Extract parameters for tool execution from task."""
        params = {}

        if "property_ids" in task.parameters and task.parameters["property_ids"]:
            params["property_id"] = task.parameters["property_ids"][0]

        if "time_period" in task.parameters:
            params["time_period"] = task.parameters["time_period"]

        if "metrics" in task.parameters:
            params["metrics"] = task.parameters["metrics"]

        return params

    def _synthesize_plan_result(self, plan: QueryPlan) -> Dict[str, Any]:
        """Synthesize final result from completed plan."""
        result = {
            "plan_id": plan.plan_id,
            "query": plan.original_query,
            "intent": plan.intent.value,
            "tasks_completed": sum(1 for task in plan.tasks if task.completed),
            "total_tasks": len(plan.tasks),
            "complexity_score": plan.complexity_score,
            "estimated_duration": plan.estimated_total_duration,
            "task_results": {}
        }

        # Collect results from completed tasks
        for task in plan.tasks:
            if task.completed and task.result:
                result["task_results"][task.task_id] = {
                    "type": task.task_type.value,
                    "result": task.result
                }

        return result

    def reset_metrics(self):
        """Reset planning metrics (for testing)."""
        self.plans_created = 0
        self.successful_plans = 0
        self.failed_plans = 0
        if self.test_mode:
            self.logger.debug("Reset query planner metrics for testing")
