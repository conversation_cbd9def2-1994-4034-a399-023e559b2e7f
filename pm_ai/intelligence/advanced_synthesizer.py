"""
Advanced response synthesis for complex property management queries.
Integrates with reasoning engine and query planner for comprehensive insights.
"""
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging
import json

from .llm_engine import LLMEng<PERSON>
from .reasoning_engine import Reasoning<PERSON>hain, ReasoningStep
from .query_planner import QueryPlan, TaskType
from ..integration.tool_orchestrator import ToolResult

class SynthesisMode(Enum):
    """Different modes of response synthesis."""
    SIMPLE = "simple"           # Basic tool result formatting
    ANALYTICAL = "analytical"   # Deep analysis with insights
    NARRATIVE = "narrative"     # Story-like comprehensive response
    EXECUTIVE = "executive"     # Executive summary format
    TECHNICAL = "technical"     # Detailed technical analysis

class ResponseSection(Enum):
    """Sections that can be included in responses."""
    EXECUTIVE_SUMMARY = "executive_summary"
    KEY_FINDINGS = "key_findings"
    DETAILED_ANALYSIS = "detailed_analysis"
    RECOMMENDATIONS = "recommendations"
    RISK_ASSESSMENT = "risk_assessment"
    NEXT_STEPS = "next_steps"
    SUPPORTING_DATA = "supporting_data"

@dataclass
class SynthesisContext:
    """Context for advanced response synthesis."""
    query: str
    intent: str
    confidence: float
    reasoning_chain: Optional[ReasoningChain] = None
    query_plan: Optional[QueryPlan] = None
    tool_results: List[ToolResult] = None
    conversation_context: Optional[Dict[str, Any]] = None
    synthesis_mode: SynthesisMode = SynthesisMode.ANALYTICAL
    required_sections: List[ResponseSection] = None
    user_preferences: Optional[Dict[str, Any]] = None

class AdvancedSynthesizer:
    """
    Advanced response synthesizer for complex property management queries.
    
    Capabilities:
    - Multi-modal synthesis (analytical, narrative, executive, technical)
    - Integration with reasoning chains and query plans
    - Adaptive response structure based on query complexity
    - Context-aware personalization
    - Progressive disclosure of information
    - Confidence-based response adjustment
    """
    
    def __init__(self, llm_engine: LLMEngine, test_mode: bool = False):
        self.llm_engine = llm_engine
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)
        
        # Response templates for different modes
        self.response_templates = self._initialize_response_templates()
        
        # Synthesis metrics
        self.synthesis_count = 0
        self.complex_synthesis_count = 0
        self.simple_synthesis_count = 0
        self.error_count = 0
        
    def _initialize_response_templates(self) -> Dict[SynthesisMode, str]:
        """Initialize response templates for different synthesis modes."""
        return {
            SynthesisMode.SIMPLE: """
            Provide a clear, concise response to the user's query based on the data provided.
            Focus on directly answering their question with the most relevant information.
            """,
            
            SynthesisMode.ANALYTICAL: """
            Provide a comprehensive analytical response that includes:
            1. Key findings from the data
            2. Insights and patterns identified
            3. Implications for property management
            4. Actionable recommendations
            Structure your response with clear sections and bullet points where appropriate.
            """,
            
            SynthesisMode.NARRATIVE: """
            Create a narrative response that tells the story of the property's performance.
            Use a conversational tone while maintaining professionalism.
            Connect different data points to create a coherent narrative about what's happening and why.
            """,
            
            SynthesisMode.EXECUTIVE: """
            Provide an executive summary format response with:
            - Executive Summary (2-3 sentences)
            - Key Metrics
            - Critical Issues (if any)
            - Recommendations
            - Next Steps
            Keep it concise but comprehensive for decision-making.
            """,
            
            SynthesisMode.TECHNICAL: """
            Provide a detailed technical analysis including:
            - Methodology used
            - Data quality assessment
            - Statistical significance
            - Confidence intervals
            - Limitations and assumptions
            - Detailed recommendations with implementation steps
            """
        }
    
    def synthesize_advanced_response(self, context: SynthesisContext) -> str:
        """
        Generate advanced response using reasoning chains and query plans.
        
        Args:
            context: SynthesisContext with all necessary information
            
        Returns:
            Comprehensive synthesized response
        """
        self.synthesis_count += 1
        
        try:
            # Determine synthesis complexity
            is_complex = self._is_complex_synthesis(context)
            
            if is_complex:
                self.complex_synthesis_count += 1
                return self._synthesize_complex_response(context)
            else:
                self.simple_synthesis_count += 1
                return self._synthesize_simple_response(context)
                
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error in advanced synthesis: {str(e)}")
            return self._generate_error_response(context, str(e))
    
    def _is_complex_synthesis(self, context: SynthesisContext) -> bool:
        """Determine if complex synthesis is needed."""
        complexity_indicators = 0

        # Check reasoning chain complexity
        if context.reasoning_chain and len(context.reasoning_chain.nodes) >= 3:
            complexity_indicators += 1

        # Check query plan complexity
        if context.query_plan and context.query_plan.complexity_score > 5:
            complexity_indicators += 1

        # Check number of tool results
        if context.tool_results and len(context.tool_results) > 1:
            complexity_indicators += 1

        # Check synthesis mode
        if context.synthesis_mode in [SynthesisMode.ANALYTICAL, SynthesisMode.EXECUTIVE, SynthesisMode.TECHNICAL]:
            complexity_indicators += 1

        # Check required sections
        if context.required_sections and len(context.required_sections) >= 2:
            complexity_indicators += 1

        return complexity_indicators >= 2
    
    def _synthesize_complex_response(self, context: SynthesisContext) -> str:
        """Synthesize complex response with multiple data sources."""
        if self.test_mode:
            self.logger.debug(f"Performing complex synthesis for query: {context.query}")
        
        # Build comprehensive prompt
        system_prompt = self._build_advanced_system_prompt(context)
        user_prompt = self._build_advanced_user_prompt(context)
        
        # Generate response with LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm_engine.create_completion(messages)
        
        # Post-process response
        final_response = self._post_process_response(response.get("content", ""), context)
        
        return final_response
    
    def _synthesize_simple_response(self, context: SynthesisContext) -> str:
        """Synthesize simple response for straightforward queries."""
        if self.test_mode:
            self.logger.debug(f"Performing simple synthesis for query: {context.query}")
        
        # Use basic template
        template = self.response_templates.get(context.synthesis_mode, self.response_templates[SynthesisMode.SIMPLE])
        
        # Build simple prompt
        user_prompt = f"""
        Query: {context.query}
        Intent: {context.intent}
        
        Tool Results:
        {self._format_tool_results_simple(context.tool_results or [])}
        
        {template}
        """
        
        messages = [
            {"role": "system", "content": "You are a helpful property management assistant."},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm_engine.create_completion(messages)
        final_response = response.get("content", "I couldn't generate a response at this time.")

        # Apply post-processing for simple responses too
        return self._post_process_response(final_response, context)
    
    def _build_advanced_system_prompt(self, context: SynthesisContext) -> str:
        """Build advanced system prompt for complex synthesis."""
        base_prompt = """You are an expert property management analyst with deep expertise in vacation rental analytics, market analysis, and strategic recommendations.

Your task is to synthesize complex data from multiple sources into a comprehensive, actionable response."""
        
        # Add mode-specific instructions
        mode_instructions = self.response_templates.get(context.synthesis_mode, "")
        
        # Add confidence handling
        confidence_instructions = ""
        if context.confidence < 0.7:
            confidence_instructions = "\nNote: The analysis confidence is moderate. Acknowledge uncertainty where appropriate and suggest additional data collection if needed."
        elif context.confidence > 0.9:
            confidence_instructions = "\nNote: The analysis confidence is high. You can make strong recommendations based on the data."
        
        # Add personalization
        personalization = ""
        if context.user_preferences:
            personalization = f"\nUser preferences: {json.dumps(context.user_preferences)}"
        
        return f"{base_prompt}\n\n{mode_instructions}{confidence_instructions}{personalization}"
    
    def _build_advanced_user_prompt(self, context: SynthesisContext) -> str:
        """Build advanced user prompt with all context."""
        prompt = f"User Query: {context.query}\n"
        prompt += f"Intent: {context.intent}\n"
        prompt += f"Confidence: {context.confidence:.2f}\n\n"
        
        # Add reasoning chain results
        if context.reasoning_chain:
            prompt += "Reasoning Chain Results:\n"
            prompt += self._format_reasoning_chain(context.reasoning_chain)
            prompt += "\n"
        
        # Add query plan results
        if context.query_plan:
            prompt += "Query Plan Execution Results:\n"
            prompt += self._format_query_plan(context.query_plan)
            prompt += "\n"
        
        # Add tool results
        if context.tool_results:
            prompt += "Tool Execution Results:\n"
            prompt += self._format_tool_results_detailed(context.tool_results)
            prompt += "\n"
        
        # Add conversation context
        if context.conversation_context:
            prompt += "Conversation Context:\n"
            prompt += json.dumps(context.conversation_context, indent=2)
            prompt += "\n"
        
        # Add section requirements
        if context.required_sections:
            prompt += f"Required Response Sections: {[section.value for section in context.required_sections]}\n"
        
        prompt += "\nPlease provide a comprehensive response that addresses the user's query using all available information."
        
        return prompt
    
    def _format_reasoning_chain(self, chain: ReasoningChain) -> str:
        """Format reasoning chain results for prompt."""
        formatted = f"Chain ID: {chain.chain_id}\n"
        formatted += f"Completed: {chain.completed}\n"
        formatted += f"Confidence: {chain.confidence:.2f}\n\n"
        
        for node in chain.nodes:
            if node.completed:
                formatted += f"Step: {node.step_type.value}\n"
                formatted += f"Description: {node.description}\n"
                if node.data_outputs:
                    formatted += f"Results: {json.dumps(node.data_outputs, indent=2)}\n"
                formatted += "\n"
        
        if chain.final_result:
            formatted += f"Final Result: {json.dumps(chain.final_result, indent=2)}\n"
        
        return formatted
    
    def _format_query_plan(self, plan: QueryPlan) -> str:
        """Format query plan results for prompt."""
        formatted = f"Plan ID: {plan.plan_id}\n"
        formatted += f"Completed: {plan.completed}\n"
        formatted += f"Complexity Score: {plan.complexity_score}\n"
        formatted += f"Estimated Duration: {plan.estimated_total_duration}s\n\n"
        
        for task in plan.tasks:
            if task.completed and task.result:
                formatted += f"Task: {task.task_type.value}\n"
                formatted += f"Description: {task.description}\n"
                formatted += f"Result: {json.dumps(task.result, indent=2)}\n\n"
        
        if plan.final_result:
            formatted += f"Plan Result: {json.dumps(plan.final_result, indent=2)}\n"
        
        return formatted
    
    def _format_tool_results_simple(self, tool_results: List[ToolResult]) -> str:
        """Format tool results for simple synthesis."""
        if not tool_results:
            return "No tool results available."
        
        formatted = ""
        for result in tool_results:
            formatted += f"- {result.tool_name}: "
            if result.success:
                formatted += f"Success - {json.dumps(result.data)}\n"
            else:
                formatted += f"Failed - {result.error_message}\n"
        
        return formatted
    
    def _format_tool_results_detailed(self, tool_results: List[ToolResult]) -> str:
        """Format tool results for detailed synthesis."""
        if not tool_results:
            return "No tool results available."
        
        formatted = ""
        for i, result in enumerate(tool_results, 1):
            formatted += f"Tool {i}: {result.tool_name}\n"
            formatted += f"Success: {result.success}\n"
            formatted += f"Execution Time: {result.execution_time:.2f}s\n"
            
            if result.success:
                formatted += f"Data: {json.dumps(result.data, indent=2)}\n"
            else:
                formatted += f"Error: {result.error_message}\n"
            
            formatted += "\n"
        
        return formatted
    
    def _post_process_response(self, response: str, context: SynthesisContext) -> str:
        """Post-process response for quality and consistency."""
        # Add confidence disclaimer if needed
        if context.confidence < 0.6:
            response += "\n\n*Note: This analysis is based on limited data. Consider gathering additional information for more comprehensive insights.*"
        
        # Add next steps suggestion for executive mode
        if context.synthesis_mode == SynthesisMode.EXECUTIVE and "next steps" not in response.lower():
            response += "\n\n**Recommended Next Steps:**\n- Review the analysis with your team\n- Consider implementing the suggested recommendations\n- Monitor key metrics for improvement"
        
        return response
    
    def _generate_error_response(self, context: SynthesisContext, error: str) -> str:
        """Generate error response for synthesis failures."""
        return f"I apologize, but I encountered an error while analyzing your query '{context.query}'. Error: {error}. Please try rephrasing your question or contact support if the issue persists."
    
    # Testing utility methods
    def get_synthesis_count(self) -> int:
        """Get total synthesis count (for testing)."""
        return self.synthesis_count
    
    def get_complex_synthesis_count(self) -> int:
        """Get complex synthesis count (for testing)."""
        return self.complex_synthesis_count
    
    def get_simple_synthesis_count(self) -> int:
        """Get simple synthesis count (for testing)."""
        return self.simple_synthesis_count
    
    def get_error_count(self) -> int:
        """Get error count (for testing)."""
        return self.error_count
    
    def reset_metrics(self):
        """Reset synthesis metrics (for testing)."""
        self.synthesis_count = 0
        self.complex_synthesis_count = 0
        self.simple_synthesis_count = 0
        self.error_count = 0
        if self.test_mode:
            self.logger.debug("Reset advanced synthesizer metrics for testing")
