# Test-specific environment configuration
# This file prevents API key conflicts between test and production environments

# Test mode configuration
PM_AI_TEST_MODE=unit  # Options: unit, integration, e2e

# OpenAI API configuration for tests
# Use a separate test API key if available, otherwise falls back to main key
OPENAI_API_KEY_TEST=  # Optional: separate test API key
PM_AI_DEBUG=true

# Cost control for integration tests
PM_AI_MAX_API_CALLS_PER_TEST_RUN=50

# Test-specific OpenAI settings
OPENAI_MODEL=gpt-4o-mini  # Use cheaper model for testing
OPENAI_MAX_TOKENS=1000    # Reduced tokens for cost control
OPENAI_TEMPERATURE=0.1    # Low temperature for consistent test results

# Test performance settings
PM_AI_TEST_TIMEOUT=30     # Maximum test execution time in seconds
PM_AI_MOCK_DELAY=0.1      # Simulated delay for mock responses

# Logging configuration for tests
PM_AI_LOG_LEVEL=DEBUG
PM_AI_LOG_FILE=test_pm_ai.log
